package com.neo.nova.domain.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * TargetDataTypeEnum使用示例
 * 展示如何使用新的targetDataTypes多选功能
 */
public class TargetDataTypeUsageExample {

    public static void main(String[] args) {
        System.out.println("TargetDataTypeEnum使用示例");
        
        // 示例1：获取所有可用的数据类型
        System.out.println("\n=== 示例1：所有可用的数据类型 ===");
        showAllAvailableTypes();
        
        // 示例2：按分类展示数据类型
        System.out.println("\n=== 示例2：按分类展示数据类型 ===");
        showTypesByCategory();
        
        // 示例3：验证数据类型代码
        System.out.println("\n=== 示例3：验证数据类型代码 ===");
        validateDataTypeCodes();
        
        // 示例4：模拟前端请求参数
        System.out.println("\n=== 示例4：模拟前端请求参数 ===");
        simulateRequestParameters();
        
        System.out.println("\n=== 所有示例完成 ===");
    }
    
    /**
     * 展示所有可用的数据类型
     */
    private static void showAllAvailableTypes() {
        System.out.println("所有可用的数据类型：");
        for (TargetDataTypeEnum enumValue : TargetDataTypeEnum.values()) {
            System.out.println("  - " + enumValue.getCode() + " (" + enumValue.getName() + ")");
        }
    }
    
    /**
     * 按分类展示数据类型
     */
    private static void showTypesByCategory() {
        System.out.println("销售额相关：");
        List<String> salesTypes = Arrays.asList(
            TargetDataTypeEnum.SALES_AMOUNT.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode()
        );
        for (String type : salesTypes) {
            System.out.println("  - " + type + " (" + TargetDataTypeEnum.getNameByCode(type) + ")");
        }
        
        System.out.println("\n毛利额相关：");
        List<String> profitAmountTypes = Arrays.asList(
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode()
        );
        for (String type : profitAmountTypes) {
            System.out.println("  - " + type + " (" + TargetDataTypeEnum.getNameByCode(type) + ")");
        }
        
        System.out.println("\n毛利率相关：");
        List<String> profitMarginTypes = Arrays.asList(
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
        );
        for (String type : profitMarginTypes) {
            System.out.println("  - " + type + " (" + TargetDataTypeEnum.getNameByCode(type) + ")");
        }
    }
    
    /**
     * 验证数据类型代码
     */
    private static void validateDataTypeCodes() {
        // 有效的代码
        List<String> validCodes = Arrays.asList(
            "salesAmount",
            "grossProfitAmount",
            "grossProfitMargin",
            "salesAmountRatio",
            "grossProfitAmountMomGrowth"
        );
        
        System.out.println("验证有效代码：");
        for (String code : validCodes) {
            boolean isValid = TargetDataTypeEnum.isValidCode(code);
            String name = TargetDataTypeEnum.getNameByCode(code);
            System.out.println("  - " + code + ": " + (isValid ? "✅ 有效" : "❌ 无效") + 
                             (name != null ? " (" + name + ")" : ""));
        }
        
        // 无效的代码
        List<String> invalidCodes = Arrays.asList(
            "invalidCode",
            "sales",
            "profit",
            null,
            ""
        );
        
        System.out.println("\n验证无效代码：");
        for (String code : invalidCodes) {
            boolean isValid = TargetDataTypeEnum.isValidCode(code);
            System.out.println("  - " + (code != null ? "'" + code + "'" : "null") + ": " + 
                             (isValid ? "✅ 有效" : "❌ 无效"));
        }
    }
    
    /**
     * 模拟前端请求参数
     */
    private static void simulateRequestParameters() {
        // 场景1：只查询基础数据（销售额、毛利额、毛利率）
        System.out.println("场景1：只查询基础数据");
        List<String> basicTypes = Arrays.asList(
            TargetDataTypeEnum.SALES_AMOUNT.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode()
        );
        printRequestExample("基础数据查询", basicTypes);
        
        // 场景2：只查询占比数据
        System.out.println("\n场景2：只查询占比数据");
        List<String> ratioTypes = Arrays.asList(
            TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode()
        );
        printRequestExample("占比数据查询", ratioTypes);
        
        // 场景3：只查询环比增长数据
        System.out.println("\n场景3：只查询环比增长数据");
        List<String> momGrowthTypes = Arrays.asList(
            TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode()
        );
        printRequestExample("环比增长数据查询", momGrowthTypes);
        
        // 场景4：自定义组合
        System.out.println("\n场景4：自定义组合");
        List<String> customTypes = Arrays.asList(
            TargetDataTypeEnum.SALES_AMOUNT.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
        );
        printRequestExample("自定义组合查询", customTypes);
        
        // 场景5：空列表（查询所有数据）
        System.out.println("\n场景5：查询所有数据（默认行为）");
        List<String> allTypes = new ArrayList<>();
        printRequestExample("查询所有数据", allTypes);
    }
    
    /**
     * 打印请求示例
     */
    private static void printRequestExample(String scenarioName, List<String> targetDataTypes) {
        System.out.println("  " + scenarioName + ":");
        
        if (targetDataTypes.isEmpty()) {
            System.out.println("    targetDataTypes: [] (空数组，表示查询所有数据类型)");
        } else {
            System.out.println("    targetDataTypes: [");
            for (int i = 0; i < targetDataTypes.size(); i++) {
                String type = targetDataTypes.get(i);
                String name = TargetDataTypeEnum.getNameByCode(type);
                String comma = (i < targetDataTypes.size() - 1) ? "," : "";
                System.out.println("      \"" + type + "\"" + comma + " // " + name);
            }
            System.out.println("    ]");
        }
        
        // 验证所有代码是否有效
        boolean allValid = true;
        for (String type : targetDataTypes) {
            if (!TargetDataTypeEnum.isValidCode(type)) {
                allValid = false;
                break;
            }
        }
        System.out.println("    验证结果: " + (allValid ? "✅ 所有代码有效" : "❌ 包含无效代码"));
    }
    
    /**
     * 工具方法：获取指定分类的数据类型
     */
    public static class DataTypeHelper {
        
        public static List<String> getSalesAmountTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.SALES_AMOUNT.getCode(),
                TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
                TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getGrossProfitAmountTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getGrossProfitMarginTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getBasicTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.SALES_AMOUNT.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode()
            );
        }
        
        public static List<String> getRatioTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode()
            );
        }
        
        public static List<String> getMomGrowthTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode()
            );
        }
        
        public static List<String> getYoyGrowthTypes() {
            return Arrays.asList(
                TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getAllTypes() {
            List<String> allTypes = new ArrayList<>();
            for (TargetDataTypeEnum enumValue : TargetDataTypeEnum.values()) {
                allTypes.add(enumValue.getCode());
            }
            return allTypes;
        }
    }
}
