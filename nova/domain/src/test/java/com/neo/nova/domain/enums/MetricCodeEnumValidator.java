package com.neo.nova.domain.enums;

/**
 * MetricCodeEnum验证器
 * 验证新增的枚举值和映射关系
 */
public class MetricCodeEnumValidator {

    public static void main(String[] args) {
        System.out.println("开始验证MetricCodeEnum...");
        
        boolean allTestsPassed = true;
        
        // 测试新增的枚举值
        allTestsPassed &= testNewEnumValues();
        
        // 测试映射关系
        allTestsPassed &= testMetricCodeMapping();
        
        // 测试表名映射
        allTestsPassed &= testTableNameMapping();
        
        // 测试复杂指标判断
        allTestsPassed &= testIsComplexMetric();
        
        // 测试所有枚举值
        allTestsPassed &= testAllEnumValues();
        
        if (allTestsPassed) {
            System.out.println("✅ 所有测试通过！");
            System.exit(0);
        } else {
            System.out.println("❌ 部分测试失败！");
            System.exit(1);
        }
    }
    
    private static boolean testNewEnumValues() {
        System.out.println("测试新增的枚举值...");
        boolean passed = true;
        
        try {
            // 测试MARKET枚举
            if (!"市场".equals(MetricCodeEnum.MARKET.getName())) {
                System.out.println("❌ MARKET名称错误: " + MetricCodeEnum.MARKET.getName());
                passed = false;
            }
            if (!"C_MARKET".equals(MetricCodeEnum.MARKET.getCode())) {
                System.out.println("❌ MARKET代码错误: " + MetricCodeEnum.MARKET.getCode());
                passed = false;
            }
            
            // 测试PRODUCT_SOURCE枚举
            if (!"产品来源".equals(MetricCodeEnum.PRODUCT_SOURCE.getName())) {
                System.out.println("❌ PRODUCT_SOURCE名称错误: " + MetricCodeEnum.PRODUCT_SOURCE.getName());
                passed = false;
            }
            if (!"D_PRODUCT_SOURCE".equals(MetricCodeEnum.PRODUCT_SOURCE.getCode())) {
                System.out.println("❌ PRODUCT_SOURCE代码错误: " + MetricCodeEnum.PRODUCT_SOURCE.getCode());
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ 新增枚举值测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ 新增枚举值测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testMetricCodeMapping() {
        System.out.println("测试MetricCodeMapping...");
        boolean passed = true;
        
        try {
            // 测试新增的映射
            if (!"marketId".equals(MetricCodeEnum.metricCodeMapping("C_MARKET"))) {
                System.out.println("❌ C_MARKET映射错误: " + MetricCodeEnum.metricCodeMapping("C_MARKET"));
                passed = false;
            }
            if (!"produceOemFlag".equals(MetricCodeEnum.metricCodeMapping("D_PRODUCT_SOURCE"))) {
                System.out.println("❌ D_PRODUCT_SOURCE映射错误: " + MetricCodeEnum.metricCodeMapping("D_PRODUCT_SOURCE"));
                passed = false;
            }
            
            // 测试现有的映射
            if (!"channelId".equals(MetricCodeEnum.metricCodeMapping("C_CHANNEL"))) {
                System.out.println("❌ C_CHANNEL映射错误: " + MetricCodeEnum.metricCodeMapping("C_CHANNEL"));
                passed = false;
            }
            if (!"customerId".equals(MetricCodeEnum.metricCodeMapping("D_CUSTOMER"))) {
                System.out.println("❌ D_CUSTOMER映射错误: " + MetricCodeEnum.metricCodeMapping("D_CUSTOMER"));
                passed = false;
            }
            
            // 测试不存在的映射
            if (MetricCodeEnum.metricCodeMapping("INVALID_CODE") != null) {
                System.out.println("❌ 无效代码应返回null: " + MetricCodeEnum.metricCodeMapping("INVALID_CODE"));
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ MetricCodeMapping测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ MetricCodeMapping测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testTableNameMapping() {
        System.out.println("测试TableNameMapping...");
        boolean passed = true;
        
        try {
            // 测试新增的表名映射
            if (!"marketName".equals(MetricCodeEnum.tableNameMapping("C_MARKET"))) {
                System.out.println("❌ C_MARKET表名映射错误: " + MetricCodeEnum.tableNameMapping("C_MARKET"));
                passed = false;
            }
            if (!"produceSourceName".equals(MetricCodeEnum.tableNameMapping("D_PRODUCT_SOURCE"))) {
                System.out.println("❌ D_PRODUCT_SOURCE表名映射错误: " + MetricCodeEnum.tableNameMapping("D_PRODUCT_SOURCE"));
                passed = false;
            }
            
            // 测试现有的表名映射
            if (!"channelName".equals(MetricCodeEnum.tableNameMapping("C_CHANNEL"))) {
                System.out.println("❌ C_CHANNEL表名映射错误: " + MetricCodeEnum.tableNameMapping("C_CHANNEL"));
                passed = false;
            }
            if (!"customerName".equals(MetricCodeEnum.tableNameMapping("D_CUSTOMER"))) {
                System.out.println("❌ D_CUSTOMER表名映射错误: " + MetricCodeEnum.tableNameMapping("D_CUSTOMER"));
                passed = false;
            }
            
            // 测试不存在的映射
            if (MetricCodeEnum.tableNameMapping("INVALID_CODE") != null) {
                System.out.println("❌ 无效代码应返回null: " + MetricCodeEnum.tableNameMapping("INVALID_CODE"));
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ TableNameMapping测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ TableNameMapping测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testIsComplexMetric() {
        System.out.println("测试IsComplexMetric...");
        boolean passed = true;
        
        try {
            // 测试复杂指标
            if (!MetricCodeEnum.isComplexMetric("C_MARKET")) {
                System.out.println("❌ C_MARKET应该是复杂指标");
                passed = false;
            }
            if (!MetricCodeEnum.isComplexMetric("C_CHANNEL")) {
                System.out.println("❌ C_CHANNEL应该是复杂指标");
                passed = false;
            }
            if (!MetricCodeEnum.isComplexMetric("C_SUPERMARKET_AREA")) {
                System.out.println("❌ C_SUPERMARKET_AREA应该是复杂指标");
                passed = false;
            }
            
            // 测试直接指标
            if (MetricCodeEnum.isComplexMetric("D_PRODUCT_SOURCE")) {
                System.out.println("❌ D_PRODUCT_SOURCE不应该是复杂指标");
                passed = false;
            }
            if (MetricCodeEnum.isComplexMetric("D_CUSTOMER")) {
                System.out.println("❌ D_CUSTOMER不应该是复杂指标");
                passed = false;
            }
            
            // 测试特殊指标
            if (MetricCodeEnum.isComplexMetric("S_ALLCOMPANY")) {
                System.out.println("❌ S_ALLCOMPANY不应该是复杂指标");
                passed = false;
            }
            
            // 测试不存在的代码
            if (MetricCodeEnum.isComplexMetric("INVALID_CODE")) {
                System.out.println("❌ 无效代码应返回false");
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ IsComplexMetric测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ IsComplexMetric测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testAllEnumValues() {
        System.out.println("测试所有枚举值...");
        boolean passed = true;
        
        try {
            for (MetricCodeEnum metric : MetricCodeEnum.values()) {
                // 检查名称
                if (metric.getName() == null || metric.getName().isEmpty()) {
                    System.out.println("❌ 枚举 " + metric.getCode() + " 的名称为空");
                    passed = false;
                }
                
                // 检查代码
                if (metric.getCode() == null || metric.getCode().isEmpty()) {
                    System.out.println("❌ 枚举 " + metric.getName() + " 的代码为空");
                    passed = false;
                }
                
                // 验证getNameByCode和queryMetric的一致性
                if (!metric.getName().equals(MetricCodeEnum.getNameByCode(metric.getCode()))) {
                    System.out.println("❌ getNameByCode不一致: " + metric.getCode());
                    passed = false;
                }
                if (!metric.equals(MetricCodeEnum.queryMetric(metric.getCode()))) {
                    System.out.println("❌ queryMetric不一致: " + metric.getCode());
                    passed = false;
                }
            }
            
            if (passed) {
                System.out.println("✅ 所有枚举值测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ 所有枚举值测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
}
