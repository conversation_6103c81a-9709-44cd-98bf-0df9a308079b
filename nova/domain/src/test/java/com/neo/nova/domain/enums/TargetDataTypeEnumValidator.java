package com.neo.nova.domain.enums;

/**
 * TargetDataTypeEnum验证器
 * 验证目标数据类型枚举的功能
 */
public class TargetDataTypeEnumValidator {

    public static void main(String[] args) {
        System.out.println("开始验证TargetDataTypeEnum...");
        
        boolean allTestsPassed = true;
        
        // 测试枚举值
        allTestsPassed &= testEnumValues();
        
        // 测试getter方法
        allTestsPassed &= testGetterMethods();
        
        // 测试静态方法
        allTestsPassed &= testStaticMethods();
        
        // 测试边界情况
        allTestsPassed &= testEdgeCases();
        
        if (allTestsPassed) {
            System.out.println("✅ 所有测试通过！");
            System.exit(0);
        } else {
            System.out.println("❌ 部分测试失败！");
            System.exit(1);
        }
    }
    
    private static boolean testEnumValues() {
        System.out.println("测试枚举值...");
        boolean passed = true;
        
        try {
            // 测试销售额相关枚举
            if (!"salesAmount".equals(TargetDataTypeEnum.SALES_AMOUNT.getCode())) {
                System.out.println("❌ SALES_AMOUNT代码错误: " + TargetDataTypeEnum.SALES_AMOUNT.getCode());
                passed = false;
            }
            if (!"销售额".equals(TargetDataTypeEnum.SALES_AMOUNT.getName())) {
                System.out.println("❌ SALES_AMOUNT名称错误: " + TargetDataTypeEnum.SALES_AMOUNT.getName());
                passed = false;
            }
            
            // 测试毛利额相关枚举
            if (!"grossProfitAmount".equals(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode())) {
                System.out.println("❌ GROSS_PROFIT_AMOUNT代码错误: " + TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode());
                passed = false;
            }
            if (!"毛利额".equals(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getName())) {
                System.out.println("❌ GROSS_PROFIT_AMOUNT名称错误: " + TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getName());
                passed = false;
            }
            
            // 测试毛利率相关枚举
            if (!"grossProfitMargin".equals(TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode())) {
                System.out.println("❌ GROSS_PROFIT_MARGIN代码错误: " + TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode());
                passed = false;
            }
            if (!"毛利率".equals(TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getName())) {
                System.out.println("❌ GROSS_PROFIT_MARGIN名称错误: " + TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getName());
                passed = false;
            }
            
            // 测试新增的占比和增长率枚举
            if (!"salesAmountRatio".equals(TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode())) {
                System.out.println("❌ SALES_AMOUNT_RATIO代码错误: " + TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode());
                passed = false;
            }
            if (!"销售额占比".equals(TargetDataTypeEnum.SALES_AMOUNT_RATIO.getName())) {
                System.out.println("❌ SALES_AMOUNT_RATIO名称错误: " + TargetDataTypeEnum.SALES_AMOUNT_RATIO.getName());
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ 枚举值测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ 枚举值测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testGetterMethods() {
        System.out.println("测试getter方法...");
        boolean passed = true;
        
        try {
            for (TargetDataTypeEnum enumValue : TargetDataTypeEnum.values()) {
                // 检查getCode()方法
                if (enumValue.getCode() == null || enumValue.getCode().isEmpty()) {
                    System.out.println("❌ " + enumValue.name() + " 的getCode()返回空值");
                    passed = false;
                }
                
                // 检查getName()方法
                if (enumValue.getName() == null || enumValue.getName().isEmpty()) {
                    System.out.println("❌ " + enumValue.name() + " 的getName()返回空值");
                    passed = false;
                }
            }
            
            if (passed) {
                System.out.println("✅ getter方法测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ getter方法测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testStaticMethods() {
        System.out.println("测试静态方法...");
        boolean passed = true;
        
        try {
            // 测试getByCode方法
            TargetDataTypeEnum result1 = TargetDataTypeEnum.getByCode("salesAmount");
            if (result1 != TargetDataTypeEnum.SALES_AMOUNT) {
                System.out.println("❌ getByCode('salesAmount')返回错误: " + result1);
                passed = false;
            }
            
            TargetDataTypeEnum result2 = TargetDataTypeEnum.getByCode("grossProfitAmount");
            if (result2 != TargetDataTypeEnum.GROSS_PROFIT_AMOUNT) {
                System.out.println("❌ getByCode('grossProfitAmount')返回错误: " + result2);
                passed = false;
            }
            
            // 测试getNameByCode方法
            String name1 = TargetDataTypeEnum.getNameByCode("salesAmount");
            if (!"销售额".equals(name1)) {
                System.out.println("❌ getNameByCode('salesAmount')返回错误: " + name1);
                passed = false;
            }
            
            String name2 = TargetDataTypeEnum.getNameByCode("grossProfitMargin");
            if (!"毛利率".equals(name2)) {
                System.out.println("❌ getNameByCode('grossProfitMargin')返回错误: " + name2);
                passed = false;
            }
            
            // 测试isValidCode方法
            if (!TargetDataTypeEnum.isValidCode("salesAmount")) {
                System.out.println("❌ isValidCode('salesAmount')应该返回true");
                passed = false;
            }
            
            if (!TargetDataTypeEnum.isValidCode("grossProfitAmount")) {
                System.out.println("❌ isValidCode('grossProfitAmount')应该返回true");
                passed = false;
            }
            
            if (TargetDataTypeEnum.isValidCode("invalidCode")) {
                System.out.println("❌ isValidCode('invalidCode')应该返回false");
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ 静态方法测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ 静态方法测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
    
    private static boolean testEdgeCases() {
        System.out.println("测试边界情况...");
        boolean passed = true;
        
        try {
            // 测试null值
            if (TargetDataTypeEnum.getByCode(null) != null) {
                System.out.println("❌ getByCode(null)应该返回null");
                passed = false;
            }
            
            if (TargetDataTypeEnum.getNameByCode(null) != null) {
                System.out.println("❌ getNameByCode(null)应该返回null");
                passed = false;
            }
            
            if (TargetDataTypeEnum.isValidCode(null)) {
                System.out.println("❌ isValidCode(null)应该返回false");
                passed = false;
            }
            
            // 测试空字符串
            if (TargetDataTypeEnum.getByCode("") != null) {
                System.out.println("❌ getByCode('')应该返回null");
                passed = false;
            }
            
            if (TargetDataTypeEnum.getNameByCode("") != null) {
                System.out.println("❌ getNameByCode('')应该返回null");
                passed = false;
            }
            
            if (TargetDataTypeEnum.isValidCode("")) {
                System.out.println("❌ isValidCode('')应该返回false");
                passed = false;
            }
            
            // 测试不存在的代码
            if (TargetDataTypeEnum.getByCode("nonExistentCode") != null) {
                System.out.println("❌ getByCode('nonExistentCode')应该返回null");
                passed = false;
            }
            
            if (TargetDataTypeEnum.getNameByCode("nonExistentCode") != null) {
                System.out.println("❌ getNameByCode('nonExistentCode')应该返回null");
                passed = false;
            }
            
            if (TargetDataTypeEnum.isValidCode("nonExistentCode")) {
                System.out.println("❌ isValidCode('nonExistentCode')应该返回false");
                passed = false;
            }
            
            if (passed) {
                System.out.println("✅ 边界情况测试通过");
            }
        } catch (Exception e) {
            System.out.println("❌ 边界情况测试异常: " + e.getMessage());
            passed = false;
        }
        
        return passed;
    }
}
