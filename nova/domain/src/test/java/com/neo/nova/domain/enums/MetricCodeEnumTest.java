package com.neo.nova.domain.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MetricCodeEnum测试类
 * 验证新增的枚举值和映射关系
 */
class MetricCodeEnumTest {

    @Test
    void testNewEnumValues() {
        // 测试新增的枚举值
        assertEquals("市场", MetricCodeEnum.MARKET.getName());
        assertEquals("产品来源", MetricCodeEnum.PRODUCT_SOURCE.getName());
        assertEquals("C_MARKET", MetricCodeEnum.MARKET.getCode());
        assertEquals("D_PRODUCT_SOURCE", MetricCodeEnum.PRODUCT_SOURCE.getCode());
    }

    @Test
    void testMetricCodeMapping() {
        // 测试新增的映射关系
        assertEquals("marketId", MetricCodeEnum.metricCodeMapping("C_MARKET"));
        assertEquals("produceOemFlag", MetricCodeEnum.metricCodeMapping("D_PRODUCT_SOURCE"));
        
        // 测试现有的映射关系
        assertEquals("channelId", MetricCodeEnum.metricCodeMapping("C_CHANNEL"));
        assertEquals("supermarketAreaId", MetricCodeEnum.metricCodeMapping("C_SUPERMARKET_AREA"));
        assertEquals("customerId", MetricCodeEnum.metricCodeMapping("D_CUSTOMER"));
        assertEquals("customerLevel", MetricCodeEnum.metricCodeMapping("D_CUSTOMER_LEVEL"));
        assertEquals("salesId", MetricCodeEnum.metricCodeMapping("D_CUSTOMER_OWNER"));
        assertEquals("departId", MetricCodeEnum.metricCodeMapping("D_DEPARTMENT"));
        assertEquals("customerName", MetricCodeEnum.metricCodeMapping("D_CUSTOMER_NAME"));
        assertEquals("produceTypeId", MetricCodeEnum.metricCodeMapping("D_PRODUCT_TYPE"));
        assertEquals("produceName", MetricCodeEnum.metricCodeMapping("D_PRODUCT_NAME"));
        
        // 测试不存在的映射
        assertNull(MetricCodeEnum.metricCodeMapping("INVALID_CODE"));
    }

    @Test
    void testTableNameMapping() {
        // 测试新增的表名映射
        assertEquals("marketName", MetricCodeEnum.tableNameMapping("C_MARKET"));
        assertEquals("produceSourceName", MetricCodeEnum.tableNameMapping("D_PRODUCT_SOURCE"));
        
        // 测试现有的表名映射
        assertEquals("channelName", MetricCodeEnum.tableNameMapping("C_CHANNEL"));
        assertEquals("supermarketAreaName", MetricCodeEnum.tableNameMapping("C_SUPERMARKET_AREA"));
        assertEquals("customerName", MetricCodeEnum.tableNameMapping("D_CUSTOMER"));
        assertEquals("customerLevelName", MetricCodeEnum.tableNameMapping("D_CUSTOMER_LEVEL"));
        assertEquals("salesName", MetricCodeEnum.tableNameMapping("D_CUSTOMER_OWNER"));
        assertEquals("departName", MetricCodeEnum.tableNameMapping("D_DEPARTMENT"));
        assertEquals("customerName", MetricCodeEnum.tableNameMapping("D_CUSTOMER_NAME"));
        assertEquals("produceTypeName", MetricCodeEnum.tableNameMapping("D_PRODUCT_TYPE"));
        assertEquals("produceName", MetricCodeEnum.tableNameMapping("D_PRODUCT_NAME"));
        
        // 测试不存在的映射
        assertNull(MetricCodeEnum.tableNameMapping("INVALID_CODE"));
    }

    @Test
    void testGetNameByCode() {
        // 测试根据代码获取名称
        assertEquals("市场", MetricCodeEnum.getNameByCode("C_MARKET"));
        assertEquals("产品来源", MetricCodeEnum.getNameByCode("D_PRODUCT_SOURCE"));
        assertEquals("渠道", MetricCodeEnum.getNameByCode("C_CHANNEL"));
        assertEquals("商超区域", MetricCodeEnum.getNameByCode("C_SUPERMARKET_AREA"));
        assertEquals("客户", MetricCodeEnum.getNameByCode("D_CUSTOMER"));
        assertEquals("客户等级", MetricCodeEnum.getNameByCode("D_CUSTOMER_LEVEL"));
        assertEquals("业务员", MetricCodeEnum.getNameByCode("D_CUSTOMER_OWNER"));
        assertEquals("部门", MetricCodeEnum.getNameByCode("D_DEPARTMENT"));
        assertEquals("客户名称", MetricCodeEnum.getNameByCode("D_CUSTOMER_NAME"));
        assertEquals("产品类型", MetricCodeEnum.getNameByCode("D_PRODUCT_TYPE"));
        assertEquals("产品名称", MetricCodeEnum.getNameByCode("D_PRODUCT_NAME"));
        
        // 测试不存在的代码
        assertNull(MetricCodeEnum.getNameByCode("INVALID_CODE"));
    }

    @Test
    void testQueryMetric() {
        // 测试查询枚举对象
        assertEquals(MetricCodeEnum.MARKET, MetricCodeEnum.queryMetric("C_MARKET"));
        assertEquals(MetricCodeEnum.PRODUCT_SOURCE, MetricCodeEnum.queryMetric("D_PRODUCT_SOURCE"));
        assertEquals(MetricCodeEnum.CHANNEL, MetricCodeEnum.queryMetric("C_CHANNEL"));
        assertEquals(MetricCodeEnum.SUPERMARKET_AREA, MetricCodeEnum.queryMetric("C_SUPERMARKET_AREA"));
        assertEquals(MetricCodeEnum.CUSTOMER, MetricCodeEnum.queryMetric("D_CUSTOMER"));
        assertEquals(MetricCodeEnum.CUSTOMER_LEVEL, MetricCodeEnum.queryMetric("D_CUSTOMER_LEVEL"));
        assertEquals(MetricCodeEnum.CUSTOMER_OWNER, MetricCodeEnum.queryMetric("D_CUSTOMER_OWNER"));
        assertEquals(MetricCodeEnum.DEPARTMENT, MetricCodeEnum.queryMetric("D_DEPARTMENT"));
        assertEquals(MetricCodeEnum.CUSTOMER_NAME, MetricCodeEnum.queryMetric("D_CUSTOMER_NAME"));
        assertEquals(MetricCodeEnum.PRODUCT_TYPE, MetricCodeEnum.queryMetric("D_PRODUCT_TYPE"));
        assertEquals(MetricCodeEnum.PRODUCT_NAME, MetricCodeEnum.queryMetric("D_PRODUCT_NAME"));
        
        // 测试不存在的代码
        assertNull(MetricCodeEnum.queryMetric("INVALID_CODE"));
    }

    @Test
    void testIsComplexMetric() {
        // 测试复杂指标判断
        assertTrue(MetricCodeEnum.isComplexMetric("C_MARKET"));
        assertTrue(MetricCodeEnum.isComplexMetric("C_CHANNEL"));
        assertTrue(MetricCodeEnum.isComplexMetric("C_SUPERMARKET_AREA"));
        
        // 测试直接指标
        assertFalse(MetricCodeEnum.isComplexMetric("D_PRODUCT_SOURCE"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_CUSTOMER"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_CUSTOMER_LEVEL"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_CUSTOMER_OWNER"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_DEPARTMENT"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_CUSTOMER_NAME"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_PRODUCT_TYPE"));
        assertFalse(MetricCodeEnum.isComplexMetric("D_PRODUCT_NAME"));
        
        // 测试特殊指标
        assertFalse(MetricCodeEnum.isComplexMetric("S_ALLCOMPANY"));
        
        // 测试不存在的代码
        assertFalse(MetricCodeEnum.isComplexMetric("INVALID_CODE"));
    }

    @Test
    void testEnumCodeTypes() {
        // 测试枚举的代码类型
        assertEquals(MetricCodeTypeEnum.COMPLEX_METRIC.getCode(), MetricCodeEnum.MARKET.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.PRODUCT_SOURCE.getCodeType());
        assertEquals(MetricCodeTypeEnum.COMPLEX_METRIC.getCode(), MetricCodeEnum.CHANNEL.getCodeType());
        assertEquals(MetricCodeTypeEnum.COMPLEX_METRIC.getCode(), MetricCodeEnum.SUPERMARKET_AREA.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.CUSTOMER.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.CUSTOMER_LEVEL.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.CUSTOMER_OWNER.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.DEPARTMENT.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.CUSTOMER_NAME.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.PRODUCT_TYPE.getCodeType());
        assertEquals(MetricCodeTypeEnum.DIRECT_METRIC.getCode(), MetricCodeEnum.PRODUCT_NAME.getCodeType());
        assertEquals(MetricCodeTypeEnum.SPECIAL_METRIC.getCode(), MetricCodeEnum.ALLCOMPANY.getCodeType());
    }

    @Test
    void testAllEnumValues() {
        // 确保所有枚举值都有对应的映射
        for (MetricCodeEnum metric : MetricCodeEnum.values()) {
            // 每个枚举都应该有名称
            assertNotNull(metric.getName(), "枚举 " + metric.getCode() + " 应该有名称");
            assertFalse(metric.getName().isEmpty(), "枚举 " + metric.getCode() + " 的名称不应为空");
            
            // 每个枚举都应该有代码
            assertNotNull(metric.getCode(), "枚举 " + metric.getName() + " 应该有代码");
            assertFalse(metric.getCode().isEmpty(), "枚举 " + metric.getName() + " 的代码不应为空");
            
            // 验证getNameByCode和queryMetric的一致性
            assertEquals(metric.getName(), MetricCodeEnum.getNameByCode(metric.getCode()));
            assertEquals(metric, MetricCodeEnum.queryMetric(metric.getCode()));
        }
    }
}
