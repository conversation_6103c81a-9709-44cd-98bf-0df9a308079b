package com.neo.nova.domain.enums;

import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/21
 **/
@Getter
public enum MetricCodeIdEnum {

    CHANNEL_MARKET("MARKET", "商超", MetricCodeEnum.CHANNEL),
    CHANNEL_VIP("VIP", "大客", MetricCodeEnum.CHANNEL),
    CHANNEL_DIRECT("DIRECT", "直营", MetricCodeEnum.CHANNEL),

    SUPERMARKET_AREA_EAST("EAST", "东部", MetricCodeEnum.SUPERMARKET_AREA),
    SUPERMARKET_AREA_WEST("WEST", "西部", MetricCodeEnum.SUPERMARKET_AREA),
    SUPERMARKET_AREA_SOUTH("SOUTH", "南部", MetricCodeEnum.SUPERMARKET_AREA),

    ;
    final String code;
    final String name;
    final MetricCodeEnum parentCode;

    MetricCodeIdEnum(String code, String name, MetricCodeEnum parentCode) {
        this.code = code;
        this.name = name;
        this.parentCode = parentCode;
    }

    public static MetricCodeIdEnum getByCode(String code) {
        for (MetricCodeIdEnum value : MetricCodeIdEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
