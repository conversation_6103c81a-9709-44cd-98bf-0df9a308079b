package com.neo.nova.domain.dto;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import lombok.Builder;
import lombok.Data;
import org.apache.poi.ss.usermodel.DataFormatter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Created by juanmao
 */
@Builder
@Data
public class TimeCondition {
    /**
     * com.neo.nova.domain.enums.PeriodTypeEnum
     */
    private Integer periodType;
    /**
     * 开始时间 like 2025-06-12
     */
    private String startDate;
    /**
     * 结束时间 like 2025-06-12
     */
    private String endDate;

    public TimeCondition() {
    }

    public TimeCondition(Integer periodType, String startDate, String endDate) {
        this.periodType = periodType;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public int tableType() {
        if (periodType == null) {
            return -1;
        }
        if (periodType == PeriodTypeEnum.MONTH.getCode()
                || periodType == PeriodTypeEnum.DOUBLE_MONTH.getCode()
                || periodType == PeriodTypeEnum.QUARTER.getCode()
                || periodType == PeriodTypeEnum.HALF_YEAR.getCode()
                || periodType == PeriodTypeEnum.YEAR.getCode()) { // 月度数据
            return 1;
        } else if (periodType == PeriodTypeEnum.CUSTOM_DAY.getCode()) { // 日度数据
            return 2;
        }
        return -1;
    }

    public void initCurrent(Integer periodType) {
        if (periodType == null) {
            return;
        }
        this.periodType = periodType;
        LocalDateTime now = LocalDateTimeUtil.now();
        if (periodType == PeriodTypeEnum.YEAR.getCode()) {
            this.startDate = now.getYear() + "-01";
            this.endDate = now.getYear() + "-12";
        } else if (periodType == PeriodTypeEnum.HALF_YEAR.getCode()) {
            if (now.getMonthValue() < 7) {
                this.startDate = now.getYear() + "-01";
                this.endDate = now.getYear() + "-06";
            } else {
                this.startDate = now.getYear() + "-07";
                this.endDate = now.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.QUARTER.getCode()) {
            if (now.getMonthValue() < 4) {
                this.startDate = now.getYear() + "-01";
                this.endDate = now.getYear() + "-03";
            } else if (now.getMonthValue() < 7) {
                this.startDate = now.getYear() + "-04";
                this.endDate = now.getYear() + "-06";
            } else if (now.getMonthValue() < 10) {
                this.startDate = now.getYear() + "-07";
                this.endDate = now.getYear() + "-09";
            } else {
                this.startDate = now.getYear() + "-10";
                this.endDate = now.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.DOUBLE_MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            if (now.getMonthValue() % 2 == 0) {
                this.startDate = now.minusMonths(1).format(dateTimeFormatter);
                this.endDate = now.format(dateTimeFormatter);
            } else {
                this.startDate = now.format(dateTimeFormatter);
                this.endDate = now.plusMonths(1).format(dateTimeFormatter);
            }
        } else if (periodType == PeriodTypeEnum.MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            this.startDate = now.format(dateTimeFormatter);
            this.endDate = now.format(dateTimeFormatter);
        } else if (periodType == PeriodTypeEnum.CUSTOM_DAY.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            this.startDate = now.format(dateTimeFormatter);
            this.endDate = now.format(dateTimeFormatter);
        }
    }


    public TimeCondition initMom() {
        if (periodType == null || startDate == null || endDate == null) {
            return this;
        }
        if (periodType == PeriodTypeEnum.YEAR.getCode()) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusYears(1);
            this.startDate = localDateTime.getYear() + "-01";
            this.endDate = localDateTime.getYear() + "-12";
        } else if (periodType == PeriodTypeEnum.HALF_YEAR.getCode()) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(6);
            if (localDateTime.getMonthValue() < 7) {
                this.startDate = localDateTime.getYear() + "-01";
                this.endDate = localDateTime.getYear() + "-06";
            } else {
                this.startDate = localDateTime.getYear() + "-07";
                this.endDate = localDateTime.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.QUARTER.getCode()) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(3);
            if (localDateTime.getMonthValue() < 4) {
                this.startDate = localDateTime.getYear() + "-01";
                this.endDate = localDateTime.getYear() + "-03";
            } else if (localDateTime.getMonthValue() < 7) {
                this.startDate = localDateTime.getYear() + "-04";
                this.endDate = localDateTime.getYear() + "-06";
            } else if (localDateTime.getMonthValue() < 10) {
                this.startDate = localDateTime.getYear() + "-07";
                this.endDate = localDateTime.getYear() + "-09";
            } else {
                this.startDate = localDateTime.getYear() + "-10";
                this.endDate = localDateTime.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.DOUBLE_MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(2);
            if (localDateTime.getMonthValue() % 2 == 0) {
                this.startDate = localDateTime.minusMonths(1).format(dateTimeFormatter);
                this.endDate = localDateTime.format(dateTimeFormatter);
            } else {
                this.startDate = localDateTime.format(dateTimeFormatter);
                this.endDate = localDateTime.plusMonths(1).format(dateTimeFormatter);
            }
        } else if (periodType == PeriodTypeEnum.MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(1);
            this.startDate = localDateTime.format(dateTimeFormatter);
            this.endDate = localDateTime.format(dateTimeFormatter);
        } else if (periodType == PeriodTypeEnum.CUSTOM_DAY.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            this.startDate = startDateTime.minusDays(endDateTime.compareTo(startDateTime)).minusDays(1).format(dateTimeFormatter);
            this.endDate = startDateTime.minusDays(1).format(dateTimeFormatter);
        }
        return this;
    }

    public TimeCondition copy() {
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setPeriodType(this.periodType);
        timeCondition.setStartDate(this.startDate);
        timeCondition.setEndDate(this.endDate);
        return timeCondition;
    }


    public TimeCondition initYoy() {
        if (periodType == null || startDate == null || endDate == null) {
            return this;
        }
        if (periodType == PeriodTypeEnum.YEAR.getCode()) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusYears(1);
            this.startDate = localDateTime.getYear() + "-01";
            this.endDate = localDateTime.getYear() + "-12";
        } else if (periodType == PeriodTypeEnum.HALF_YEAR.getCode()) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(12);
            if (localDateTime.getMonthValue() < 7) {
                this.startDate = localDateTime.getYear() + "-01";
                this.endDate = localDateTime.getYear() + "-06";
            } else {
                this.startDate = localDateTime.getYear() + "-07";
                this.endDate = localDateTime.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.QUARTER.getCode()) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(12);
            if (localDateTime.getMonthValue() < 4) {
                this.startDate = localDateTime.getYear() + "-01";
                this.endDate = localDateTime.getYear() + "-03";
            } else if (localDateTime.getMonthValue() < 7) {
                this.startDate = localDateTime.getYear() + "-04";
                this.endDate = localDateTime.getYear() + "-06";
            } else if (localDateTime.getMonthValue() < 10) {
                this.startDate = localDateTime.getYear() + "-07";
                this.endDate = localDateTime.getYear() + "-09";
            } else {
                this.startDate = localDateTime.getYear() + "-10";
                this.endDate = localDateTime.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.DOUBLE_MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(12);
            if (localDateTime.getMonthValue() % 2 == 0) {
                this.startDate = localDateTime.minusMonths(1).format(dateTimeFormatter);
                this.endDate = localDateTime.format(dateTimeFormatter);
            } else {
                this.startDate = localDateTime.format(dateTimeFormatter);
                this.endDate = localDateTime.plusMonths(1).format(dateTimeFormatter);
            }
        } else if (periodType == PeriodTypeEnum.MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime localDateTime = startDateTime.minusMonths(12);
            this.startDate = localDateTime.format(dateTimeFormatter);
            this.endDate = localDateTime.format(dateTimeFormatter);
        } else if (periodType == PeriodTypeEnum.CUSTOM_DAY.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            this.startDate = startDateTime.minusYears(1).format(dateTimeFormatter);
            this.endDate = endDateTime.minusYears(1).format(dateTimeFormatter);
        }
        return this;
    }

    public static void main(String[] args) {

        System.out.println(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));


//        TimeCondition timeCondition = new TimeCondition();
//        timeCondition.initCurrent(0);
//        System.out.println(timeCondition);
//        timeCondition.initMom();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(1);
//        System.out.println(timeCondition);
//        timeCondition.initMom();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(2);
//        System.out.println(timeCondition);
//        timeCondition.initMom();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(3);
//        System.out.println(timeCondition);
//        timeCondition.initMom();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(4);
//        System.out.println(timeCondition);
//        timeCondition.initMom();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(9);
//        System.out.println(timeCondition);
//        timeCondition.initMom();
//        System.out.println(timeCondition);
//
//
//        timeCondition.initCurrent(0);
//        System.out.println(timeCondition);
//        timeCondition.initYoy();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(1);
//        System.out.println(timeCondition);
//        timeCondition.initYoy();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(2);
//        System.out.println(timeCondition);
//        timeCondition.initYoy();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(3);
//        System.out.println(timeCondition);
//        timeCondition.initYoy();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(4);
//        System.out.println(timeCondition);
//        timeCondition.initYoy();
//        System.out.println(timeCondition);
//        timeCondition.initCurrent(9);
//        System.out.println(timeCondition);
//        timeCondition.initYoy();
//        System.out.println(timeCondition);


    }

    @Override
    public String toString() {
        return "TimeCondition{" +
                "periodType=" + periodType +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
