package com.neo.nova.domain.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.neo.nova.domain.enums.MetricCodeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by juanmao
 *
 * @since 2019-06-04
 */
@Data
public class Table {
    /**
     * 标题
     */
    private String title;

    /**
     * 所有列
     */
    private List<List<Column>> columns = Lists.newArrayList();
    /**
     * 数据,按行存储
     */
    private List<Map<String, Cell>> data = Lists.newArrayList();

    /**
     * 当前页
     */
    private Long currentPage = 1L;
    /**
     * 总共页码
     */
    private Long totalPage;
    /**
     * 总数
     */
    private Long total;
    /**
     * 页面大小
     */
    private Long pageSize;

    /**
     * 是否支持分页
     */
    private boolean pageable = true;

    public Table addToFirstColumn(Column column) {
        if (columns.isEmpty()) {
            columns.add(Lists.newArrayList());
        }
        columns.get(0).add(column);
        return this;
    }

    public Table addRow(Map<String, Cell> cellMap) {
        data.add(cellMap);
        return this;
    }

    public Table addToLastRow(String key, Cell cell) {
        if (data.isEmpty()) {
            data.add(Maps.newHashMap());
        }
        data.get(data.size() - 1).put(key, cell);
        return this;
    }


    //以下方法仅为方便使用

    public Table addMetricCodeColumn(String metricCode) {
        this.addToFirstColumn(Column.builder()
                .key(MetricCodeEnum.tableNameMapping(metricCode))
                .title(MetricCodeEnum.getNameByCode(metricCode)).build());
        return this;
    }

    //格式化问题先写死了
    public Table addRecord(Map<String, Object> record, List<String> amountColumns) {
        data.add(new HashMap<>());
        record.forEach((key, value) -> {
            if (amountColumns != null && amountColumns.contains(key)) {
                this.addToLastRow(key, Cell.builder().value(new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP)).build());
            } else {
                this.addToLastRow(key, Cell.builder().value(value).build());
            }
        });
        return this;
    }


}
