package com.neo.nova.domain.enums;

import java.util.Arrays;

/**
 * 目标数据类型枚举
 * 用于指定查询的数据对象类型，支持多选
 *
 * <AUTHOR>
 * @since 2025/8/14
 **/
public enum TargetDataTypeEnum {

    SALES_AMOUNT("salesAmount", "销售额"),
    SALES_AMOUNT_RATIO("salesAmountRatio", "销售额占比"),
    SALES_AMOUNT_MOM_GROWTH("salesAmountMomGrowth", "销售额环比增长"),
    SALES_AMOUNT_YOY_GROWTH("salesAmountYoyGrowth", "销售额同比增长"),

    GROSS_PROFIT_AMOUNT("grossProfitAmount", "毛利额"),
    GROSS_PROFIT_AMOUNT_RATIO("grossProfitAmountRatio", "毛利额占比"),
    GROSS_PROFIT_AMOUNT_MOM_GROWTH("grossProfitAmountMomGrowth", "毛利额环比增长"),
    GROSS_PROFIT_AMOUNT_YOY_GROWTH("grossProfitAmountYoyGrowth", "毛利额同比增长"),

    GROSS_PROFIT_MARGIN("grossProfitMargin", "毛利率"),
    GROSS_PROFIT_MARGIN_MOM_GROWTH("grossProfitMarginMomGrowth", "毛利率环比增长"),
    GROSS_PROFIT_MARGIN_YOY_GROWTH("grossProfitMarginYoyGrowth", "毛利率同比增长"),
    ;

    private final String code;
    private final String name;

    TargetDataTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举对象，如果不存在则返回null
     */
    public static TargetDataTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据代码获取名称
     *
     * @param code 代码
     * @return 名称，如果不存在则返回null
     */
    public static String getNameByCode(String code) {
        TargetDataTypeEnum enumItem = getByCode(code);
        return enumItem != null ? enumItem.getName() : null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
