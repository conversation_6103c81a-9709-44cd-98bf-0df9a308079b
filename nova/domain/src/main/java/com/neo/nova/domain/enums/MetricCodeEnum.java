package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/7/3
 **/
@Getter
public enum MetricCodeEnum {

    //直接指标，不带后缀默认使用ID

    //公司
    DEPARTMENT("D_DEPARTMENT", "部门", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    USER("D_USER", "人员", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    //产品
    PRODUCT("D_PRODUCT", "产品", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    PRODUCT_TYPE("D_PRODUCT_TYPE", "产品类型", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    PRODUCT_NAME("D_PRODUCT_NAME", "产品名称", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    //客户
    CUSTOMER("D_CUSTOMER", "客户", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    CUSTOMER_TYPE("D_CUSTOMER_TYPE", "客户类型", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    CUSTOMER_SALES_REGION("D_CUSTOMER_SALES_REGION", "客户销售区域", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    CUSTOMER_ADMIN_REGION("D_CUSTOMER_ADMIN_REGION", "客户行政区域", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    CUSTOMER_LEVEL("D_CUSTOMER_LEVEL", "客户等级", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    CUSTOMER_OWNER("D_CUSTOMER_OWNER", "业务员", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),
    CUSTOMER_NAME("D_CUSTOMER_NAME", "客户名称", MetricCodeTypeEnum.DIRECT_METRIC.getCode()),

    //由直接指标聚合而成，未来修改为用户自定义。
    CHANNEL("C_CHANNEL", "渠道", MetricCodeTypeEnum.COMPLEX_METRIC.getCode()),
    SUPERMARKET_AREA("C_SUPERMARKET_AREA", "商超区域", MetricCodeTypeEnum.COMPLEX_METRIC.getCode()),

    //代码里要特判的
    ALLCOMPANY("S_ALLCOMPANY", "全公司", MetricCodeTypeEnum.SPECIAL_METRIC.getCode()),


    ;
    final String code;
    final String name;
    final int codeType;

    MetricCodeEnum(String code, String name, int codeType) {
        this.code = code;
        this.name = name;
        this.codeType = codeType;
    }

    public static String getNameByCode(String code) {
        for (MetricCodeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public static MetricCodeEnum queryMetric(String code) {
        for (MetricCodeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isComplexMetric(String metricCode) {
        MetricCodeEnum metric = MetricCodeEnum.queryMetric(metricCode);
        return metric != null && metric.getCodeType() == MetricCodeTypeEnum.COMPLEX_METRIC.getCode();
    }

    public static String metricCodeMapping(String metricCode) {
        return switch (metricCode) {
            case "C_CHANNEL" -> "channelId";
            case "C_SUPERMARKET_AREA" -> "supermarketAreaId";
            case "D_CUSTOMER" -> "customerId";
            case "D_CUSTOMER_SALES_REGION" -> "salesRegionId";
            case "D_CUSTOMER_ADMIN_REGION" -> "adminRegionId";
            case "D_CUSTOMER_LEVEL" -> "customerLevel";
            case "D_CUSTOMER_OWNER" -> "salesId";
            case "D_USER" -> "salesId";
            case "D_DEPARTMENT" -> "departId";
            case "D_CUSTOMER_NAME" -> "customerName";
            case "D_PRODUCT_TYPE" -> "produceTypeId";
            case "D_PRODUCT_NAME" -> "produceName";
            default -> null;
        };
    }


    public static String tableNameMapping(String metricCode) {
        return switch (metricCode) {
            case "C_CHANNEL" -> "channelName";
            case "C_SUPERMARKET_AREA" -> "supermarketAreaName";
            case "D_CUSTOMER" -> "customerName";
            case "D_CUSTOMER_SALES_REGION" -> "salesRegionName";
            case "D_CUSTOMER_ADMIN_REGION" -> "adminRegionName";
            case "D_CUSTOMER_LEVEL" -> "customerLevelName";
            case "D_CUSTOMER_OWNER" -> "salesName";
            case "D_USER" -> "salesName";
            case "D_DEPARTMENT" -> "departName";
            case "D_CUSTOMER_NAME" -> "customerName";
            case "D_PRODUCT_TYPE" -> "produceTypeName";
            case "D_PRODUCT_NAME" -> "produceName";
            default -> null;
        };
    }
}
