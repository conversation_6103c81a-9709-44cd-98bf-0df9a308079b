package com.neo.nova.adapter.controller;

import com.neo.api.SingleResponse;
import com.neo.nova.app.service.EarlyWarningService;
import com.neo.nova.app.vo.ChooseOption;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.session.annotation.NeedLogin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 预警
 */
@RestController
@RequestMapping("/earlywarning")
public class EarlyWarningController {

    @Autowired
    private EarlyWarningService earlyWarningService;
    /**
     * 客户预警列表
     *
     * @param dataFormQueryVO
     * @return
     */
    @PostMapping("/list")
    @NeedLogin
    public SingleResponse<DataFormVO> list(@RequestBody DataFormQueryVO dataFormQueryVO) {
        return SingleResponse.buildSuccess(earlyWarningService.list(dataFormQueryVO));
    }

    /**
     * 获取预警类型
     * @return
     */
    @GetMapping("/getAlarmTypes")
    public SingleResponse<List<ChooseOption>> getAlarmTypes() {
        List<ChooseOption> list = new ArrayList<>();
        list.add(new ChooseOption( "客户无人管理","5"));
        list.add(new ChooseOption( "大客一周未下单","6"));
        list.add(new ChooseOption( "大客单产品15天未下单","7"));
        return SingleResponse.buildSuccess(list);
    }

}
