package com.neo.nova.adapter.controller;

import com.neo.nova.app.sync.DataSyncScheduler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据同步控制器
 * 
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
@RestController
@RequestMapping("/sync")
public class DataSyncController {
    
    @Autowired
    private DataSyncScheduler dataSyncScheduler;
    
    /**
     * 手动触发数据同步
     */
    @GetMapping("/manual")
    public String manualSync(Long tenantId) {
        try {
            dataSyncScheduler.manualSync(tenantId);
            return "数据同步任务已触发";
        } catch (Exception e) {
            log.error("手动触发数据同步失败", e);
            return "数据同步任务触发失败：" + e.getMessage();
        }
    }
}
