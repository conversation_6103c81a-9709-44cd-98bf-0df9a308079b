package com.neo.nova.adapter.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.neo.api.SingleResponse;
import com.neo.nova.app.helper.DynamicExcelHelper;
import com.neo.nova.app.service.CustomerSalesService;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.nova.app.vo.PieChartVO;
import com.neo.nova.app.vo.SalesReportQueryVO;
import com.neo.nova.domain.excelExport.StatisticsExport;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 销售统计
 *
 * <AUTHOR>
 * @since 2025/7/3
 **/
@RestController
@RequestMapping("/customer/sales")
public class CustomerSalesController {

    @Resource
    private CustomerSalesService customerSalesService;

    /**
     * 销售统计看板
     *
     * @param dataFormQueryVO
     * @return
     */
    @PostMapping("/statistics")
    @NeedLogin
    public SingleResponse<DataFormVO> statistics(@RequestBody DataFormQueryVO dataFormQueryVO) {
        dataFormQueryVO.setTenantId(SessionContextHolder.getTenantId());
        return SingleResponse.buildSuccess(customerSalesService.statistics(dataFormQueryVO));
    }

    /**
     * 销售趋势看板
     *
     * @param dataFormQueryVO
     * @return
     */
    @PostMapping("/trends")
    @NeedLogin
    public SingleResponse<DataFormVO> trends(@RequestBody DataFormQueryVO dataFormQueryVO) {
        dataFormQueryVO.setTenantId(SessionContextHolder.getTenantId());
        return SingleResponse.buildSuccess(customerSalesService.trends(dataFormQueryVO));
    }

    /**
     * 饼图数据统计
     *
     * @param currentTime 时间参数，格式：YYYY-MM（例如：202501表示2025年1月）
     * @return 饼图数据，包含年度和月度的目标、实际值、完成率等信息
     */
    @PostMapping("/pieChart")
    @NeedLogin
    public SingleResponse<PieChartVO> pieChart(@RequestParam("currentTime") String currentTime) {
        try {
            PieChartVO result = customerSalesService.pieChart(SessionContextHolder.getTenantId(), currentTime);
            return SingleResponse.buildSuccess(result);
        } catch (Exception e) {
            return SingleResponse.buildFailure("CHART_ERROR", "饼图数据查询失败：" + e.getMessage());
        }
    }


    @PostMapping("/statisticsexport")
    @NeedLogin
    public void statisticsExport(HttpServletResponse response, @RequestBody DataFormQueryVO dataFormQueryVO) throws IOException {
        dataFormQueryVO.setTenantId(SessionContextHolder.getTenantId());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        //设置Mime-Type
        response.setContentType("application/vnd.ms-excel");
        // 设置下载文件名
        String fileName = "销售统计" + formatter.format(date) + "导出.xlsx";
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        //设置下载默认文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        List<StatisticsExport> statisticsExports = customerSalesService.StatisticsExport(dataFormQueryVO);
        // 写入Excel
        EasyExcelFactory.write(response.getOutputStream(), StatisticsExport.class).sheet("销售统计").doWrite(statisticsExports);
    }

    /**
     * 销售趋势Excel导出（动态列）
     *
     * @param response
     * @param dataFormQueryVO
     * @throws IOException
     */
    @PostMapping("/trendsexport")
    @NeedLogin
    public void trendsExport(HttpServletResponse response, @RequestBody DataFormQueryVO dataFormQueryVO) throws IOException {
        dataFormQueryVO.setTenantId(SessionContextHolder.getTenantId());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        //设置Mime-Type
        response.setContentType("application/vnd.ms-excel");
        // 设置下载文件名
        String fileName = "销售趋势" + formatter.format(date) + "导出.xlsx";
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        //设置下载默认文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        List<StatisticsExport> trendsExports = customerSalesService.trendsExport(dataFormQueryVO);
        // 写入Excel
        EasyExcelFactory.write(response.getOutputStream(), StatisticsExport.class).sheet("销售趋势").doWrite(trendsExports);
    }

    /**
     * 动态销售趋势Excel导出
     * 根据查询条件动态生成Excel列结构
     * 固定包含：时间、销售额
     * 动态包含：查询条件对应的分组列
     *
     * @param response
     * @param dataFormQueryVO
     * @throws IOException
     */
    @PostMapping("/dynamicexport")
    @NeedLogin
    public void dynamicExport(HttpServletResponse response, @RequestBody DataFormQueryVO dataFormQueryVO) throws IOException {
        dataFormQueryVO.setTenantId(SessionContextHolder.getTenantId());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();

        //设置Mime-Type
        response.setContentType("application/vnd.ms-excel");
        // 设置下载文件名
        String fileName = "动态销售统计" + formatter.format(date) + "导出.xlsx";
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        //设置下载默认文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        // 获取动态数据
        List<Map<String, Object>> dynamicData = customerSalesService.getDynamicExportData(dataFormQueryVO);

        // 构建列映射
        Map<String, String> columnMapping = DynamicExcelHelper.buildColumnMapping(dataFormQueryVO.getQueryOptions());

        // 写入动态Excel
        DynamicExcelHelper.writeDynamicExcel(response.getOutputStream(), dynamicData, columnMapping, "动态销售统计");
    }

    /**
     * 销售报表查询
     * 查询指定月份的销售数据，返回区域、负责人、门店、毛利额、订货额、实际销售额等信息
     *
     * @param queryVO 查询条件
     * @return 销售报表数据
     */
    @PostMapping("/salesReport")
    @NeedLogin
    public SingleResponse<DataFormVO> salesReport(@RequestBody SalesReportQueryVO queryVO) {
        queryVO.setUserId(SessionContextHolder.getUserId());
        queryVO.setTenantId(SessionContextHolder.getTenantId());
        return SingleResponse.buildSuccess(customerSalesService.salesReport(queryVO));
    }

}
