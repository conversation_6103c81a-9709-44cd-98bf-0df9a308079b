# TargetDataTypes 多选功能 API 文档

## 概述

`TargetDataTypes` 是一个新增的查询参数，允许前端指定要查询和展示的数据类型。这个功能支持多选，让用户可以灵活地选择需要查看的数据指标。

## 枚举定义

### TargetDataTypeEnum

| 枚举值 | 代码 | 名称 | 说明 |
|--------|------|------|------|
| SALES_AMOUNT | salesAmount | 销售额 | 基础销售金额 |
| SALES_AMOUNT_RATIO | salesAmountRatio | 销售额占比 | 占当前查询条件总销量的比例 |
| SALES_AMOUNT_MOM_GROWTH | salesAmountMomGrowth | 销售额环比增长 | 占上一周期的增长比例 |
| SALES_AMOUNT_YOY_GROWTH | salesAmountYoyGrowth | 销售额同比增长 | 占去年同期的增长比例 |
| GROSS_PROFIT_AMOUNT | grossProfitAmount | 毛利额 | 基础毛利金额 |
| GROSS_PROFIT_AMOUNT_RATIO | grossProfitAmountRatio | 毛利额占比 | 占当前查询条件总毛利额的比例 |
| GROSS_PROFIT_AMOUNT_MOM_GROWTH | grossProfitAmountMomGrowth | 毛利额环比增长 | 占上一周期的增长比例 |
| GROSS_PROFIT_AMOUNT_YOY_GROWTH | grossProfitAmountYoyGrowth | 毛利额同比增长 | 占去年同期的增长比例 |
| GROSS_PROFIT_MARGIN | grossProfitMargin | 毛利率 | 毛利率百分比 |
| GROSS_PROFIT_MARGIN_MOM_GROWTH | grossProfitMarginMomGrowth | 毛利率环比增长 | 占上一周期的增长比例 |
| GROSS_PROFIT_MARGIN_YOY_GROWTH | grossProfitMarginYoyGrowth | 毛利率同比增长 | 占去年同期的增长比例 |

## API 接口

### 请求参数

在 `DataFormQueryVO` 中新增了 `targetDataTypes` 字段：

```json
{
  "queryOptions": {
    "C_CHANNEL": ["JOINT_SUPERMARKET"],
    "C_MARKET": ["GANZHOU"]
  },
  "timeCondition": {
    "periodType": 1,
    "startDate": "2025-01",
    "endDate": "2025-01"
  },
  "targetDataTypes": [
    "salesAmount",
    "salesAmountRatio",
    "grossProfitMargin"
  ],
  "pageIndex": 1,
  "pageSize": 10
}
```

### 字段说明

- **targetDataTypes**: `List<String>` (可选)
  - 要查询的数据类型列表
  - 如果为空或null，则默认查询所有数据类型
  - 支持多选，可以任意组合不同的数据类型
  - 无效的数据类型代码会被忽略，不会导致接口报错

## 使用场景

### 场景1：只查询基础数据

```json
{
  "targetDataTypes": [
    "salesAmount",
    "grossProfitAmount", 
    "grossProfitMargin"
  ]
}
```

### 场景2：只查询占比数据

```json
{
  "targetDataTypes": [
    "salesAmountRatio",
    "grossProfitAmountRatio"
  ]
}
```

### 场景3：只查询增长率数据

```json
{
  "targetDataTypes": [
    "salesAmountMomGrowth",
    "salesAmountYoyGrowth",
    "grossProfitAmountMomGrowth",
    "grossProfitAmountYoyGrowth",
    "grossProfitMarginMomGrowth",
    "grossProfitMarginYoyGrowth"
  ]
}
```

### 场景4：自定义组合

```json
{
  "targetDataTypes": [
    "salesAmount",
    "salesAmountRatio",
    "grossProfitMargin",
    "grossProfitMarginYoyGrowth"
  ]
}
```

### 场景5：查询所有数据（默认行为）

```json
{
  "targetDataTypes": []
}
```

或者不传递 `targetDataTypes` 字段。

## 响应结果

响应的表格数据会根据 `targetDataTypes` 参数动态调整列结构：

```json
{
  "code": "SUCCESS",
  "data": {
    "table": {
      "columns": [
        {
          "key": "channelName",
          "title": "渠道",
          "isSort": false
        },
        {
          "key": "salesAmount",
          "title": "销售额",
          "isSort": true
        },
        {
          "key": "salesAmountRatio",
          "title": "销售额占比",
          "isSort": true
        },
        {
          "key": "grossProfitMargin",
          "title": "毛利率",
          "isSort": true
        }
      ],
      "records": [
        {
          "channelName": "联营商超",
          "salesAmount": 1000000,
          "salesAmountRatio": 65.5,
          "grossProfitMargin": 25.8
        }
      ],
      "pageSize": 10,
      "currentPage": 1,
      "totalPage": 1,
      "total": 1
    }
  }
}
```

## 数据类型分组

为了方便前端使用，可以按以下方式对数据类型进行分组：

### 销售额相关
- `salesAmount` - 销售额
- `salesAmountRatio` - 销售额占比
- `salesAmountMomGrowth` - 销售额环比增长
- `salesAmountYoyGrowth` - 销售额同比增长

### 毛利额相关
- `grossProfitAmount` - 毛利额
- `grossProfitAmountRatio` - 毛利额占比
- `grossProfitAmountMomGrowth` - 毛利额环比增长
- `grossProfitAmountYoyGrowth` - 毛利额同比增长

### 毛利率相关
- `grossProfitMargin` - 毛利率
- `grossProfitMarginMomGrowth` - 毛利率环比增长
- `grossProfitMarginYoyGrowth` - 毛利率同比增长

### 按指标类型分组
- **基础指标**: `salesAmount`, `grossProfitAmount`, `grossProfitMargin`
- **占比指标**: `salesAmountRatio`, `grossProfitAmountRatio`
- **环比增长**: `salesAmountMomGrowth`, `grossProfitAmountMomGrowth`, `grossProfitMarginMomGrowth`
- **同比增长**: `salesAmountYoyGrowth`, `grossProfitAmountYoyGrowth`, `grossProfitMarginYoyGrowth`

## 错误处理

- 如果传入无效的数据类型代码，系统会自动忽略无效值，不会抛出异常
- 如果所有传入的数据类型代码都无效，则默认查询所有数据类型
- 空数组 `[]` 和 `null` 都表示查询所有数据类型

## 前端实现建议

### 1. 数据类型选择器

```javascript
const dataTypeOptions = [
  {
    label: '销售额相关',
    children: [
      { value: 'salesAmount', label: '销售额' },
      { value: 'salesAmountRatio', label: '销售额占比' },
      { value: 'salesAmountMomGrowth', label: '销售额环比增长' },
      { value: 'salesAmountYoyGrowth', label: '销售额同比增长' }
    ]
  },
  {
    label: '毛利额相关',
    children: [
      { value: 'grossProfitAmount', label: '毛利额' },
      { value: 'grossProfitAmountRatio', label: '毛利额占比' },
      { value: 'grossProfitAmountMomGrowth', label: '毛利额环比增长' },
      { value: 'grossProfitAmountYoyGrowth', label: '毛利额同比增长' }
    ]
  },
  {
    label: '毛利率相关',
    children: [
      { value: 'grossProfitMargin', label: '毛利率' },
      { value: 'grossProfitMarginMomGrowth', label: '毛利率环比增长' },
      { value: 'grossProfitMarginYoyGrowth', label: '毛利率同比增长' }
    ]
  }
];
```

### 2. 快捷选择按钮

```javascript
const quickSelections = {
  '基础数据': ['salesAmount', 'grossProfitAmount', 'grossProfitMargin'],
  '占比数据': ['salesAmountRatio', 'grossProfitAmountRatio'],
  '环比增长': ['salesAmountMomGrowth', 'grossProfitAmountMomGrowth', 'grossProfitMarginMomGrowth'],
  '同比增长': ['salesAmountYoyGrowth', 'grossProfitAmountYoyGrowth', 'grossProfitMarginYoyGrowth'],
  '全部数据': [] // 空数组表示查询所有
};
```

## 注意事项

1. **向后兼容**: 不传递 `targetDataTypes` 参数时，系统会保持原有行为，查询所有数据类型
2. **性能优化**: 选择较少的数据类型可以减少计算量，提高查询性能
3. **数据一致性**: 环比和同比增长率的计算需要历史数据，如果历史数据不存在，增长率会显示为0
4. **权限控制**: 后续可以根据用户权限限制可选择的数据类型
