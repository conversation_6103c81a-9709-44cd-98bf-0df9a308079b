package com.neo.nova.app.validator;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.request.PerformanceCreateRequest;
import com.neo.nova.domain.dto.PerformancePlanDTO;
import com.neo.nova.domain.dto.PerformanceTargetTreeDTO;
import com.neo.nova.domain.entity.PerformancePlan;
import com.neo.nova.domain.entity.PerformanceTarget;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.MetricDataTypeEnum;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import com.neo.nova.infrastructure.mapper.PerformancePlanMapper;
import com.neo.nova.infrastructure.mapper.PerformanceTargetMapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;

/**
 * 业绩目标创建请求参数校验器
 *
 * <AUTHOR> Assistant
 * @since 2025/7/21
 */
@Component
public class PerformanceCreateRequestValidator {

    @Resource
    private PerformancePlanMapper performancePlanMapper;

    @Resource
    private PerformanceTargetMapper performanceTargetMapper;

    /**
     * 校验业绩目标创建请求的所有参数
     *
     * @param request 创建请求
     * @return 校验后的计划ID
     */
    public Long validateAndGetPlanId(PerformanceCreateRequest request) {
        if (request.getTargets() == null) {
            request.setTargets(new ArrayList<>());
        }
        // 1. 基础参数校验
        validateBasicParameters(request);

        // 2. 计划相关校验

        return validateAndHandlePlan(request);
    }

    /**
     * 校验基础参数
     */
    private void validateBasicParameters(PerformanceCreateRequest request) {
        List<PerformanceTargetTreeDTO> targets = request.getTargets();
        if (request.getParentId() == null) {
            throw new IllegalArgumentException("父级目标ID不能为空");
        }
        // 校验 targets 不为空
        if (targets.isEmpty()) {
            return;
        }

        // 校验所有需要写入数据库的字段是否必填
        for (PerformanceTargetTreeDTO target : targets) {
            validateTargetRequiredFields(target);
        }
    }

    /**
     * 校验单个目标的必填字段
     */
    private void validateTargetRequiredFields(PerformanceTargetTreeDTO target) {
        if (target.getTargetName() == null || target.getTargetName().trim().isEmpty()) {
            throw new BizCustomException(100, PerformanceValidationMessages.TARGET_NAME_EMPTY);
        }

        if (target.getOwnerId() == null || target.getOwnerId() <= 0) {
            if (MetricCodeEnum.USER.getCode().equals(target.getMetricCode())) {
                throw new BizCustomException(100, PerformanceValidationMessages.TARGET_OWNER_INVALID);
            }
        }

        if (target.getUnit() == null || target.getUnit().trim().isEmpty()) {
            throw new BizCustomException(100, PerformanceValidationMessages.TARGET_UNIT_EMPTY);
        }

        if (target.getTargetValue() == null) {
            throw new BizCustomException(100, PerformanceValidationMessages.TARGET_VALUE_EMPTY);
        }

        if (target.getTargetValue().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizCustomException(100, PerformanceValidationMessages.TARGET_VALUE_INVALID);
        }
    }

    /**
     * 校验并处理计划相关参数
     *
     * @param request 创建请求
     * @return 计划ID
     */
    private Long validateAndHandlePlan(PerformanceCreateRequest request) {
        Long planId = request.getPlanId();

        if (planId == null) {
            // 校验新建计划的参数
            return validateNewPlanParameters(request.getPlan());
        } else {
            // 校验现有计划ID
            validateExistingPlanId(planId);
            return planId;
        }
    }

    /**
     * 校验新建计划的参数
     */
    private Long validateNewPlanParameters(PerformancePlanDTO plan) {
        if (plan == null || plan.getPeriodType() == null ||
                plan.getPeriodStart() == null || plan.getPeriodEnd() == null) {
            throw new BizCustomException(100, PerformanceValidationMessages.PLAN_INFO_MISSING);
        }

        // 校验 periodType 是否属于 PeriodTypeEnum
        if (Arrays.stream(PeriodTypeEnum.values())
                .map(PeriodTypeEnum::getCode)
                .noneMatch(code -> code.equals(plan.getPeriodType()))) {
            throw new BizCustomException(100,
                    PerformanceValidationMessages.formatPeriodTypeError(plan.getPeriodType()));
        }

        // 校验时间范围是否合法：开始时间不能晚于结束时间
        Long startTime = PeriodTypeEnum.convertStartTime(plan.getPeriodType(), plan.getPeriodStart());
        Long endTime = PeriodTypeEnum.convertEndTime(plan.getPeriodType(), plan.getPeriodEnd());
        if (startTime > endTime) {
            throw new BizCustomException(100, PerformanceValidationMessages.PERIOD_START_INVALID);
        }

        // 返回null表示需要创建新计划
        return null;
    }

    /**
     * 校验现有计划ID是否存在
     */
    private void validateExistingPlanId(Long planId) {
        PerformancePlan existingPlan = performancePlanMapper.selectOne(
                new LambdaQueryWrapper<PerformancePlan>()
                        .eq(PerformancePlan::getId, planId)
                        .eq(PerformancePlan::getIsDeleted, 0));

        if (existingPlan == null) {
            throw new BizCustomException(100,
                    PerformanceValidationMessages.formatPlanIdError(planId));
        }
    }

    /**
     * 校验目标业务逻辑
     */
    public void validateTargetBusinessLogic(List<PerformanceTargetTreeDTO> targets, Long planId) {
        Set<Long> parentIds = new HashSet<>();
        Set<String> unionKeys = new HashSet<>();

        for (PerformanceTargetTreeDTO target : targets) {
            // 校验 parentId 唯一性
            validateParentIdUniqueness(target, parentIds);

            // 校验不能有子节点（当前仅支持单层级）
            validateNoChildren(target);

            // 校验目标唯一性
            validateTargetUniqueness(target, planId, unionKeys);
        }
    }

    /**
     * 校验父目标ID唯一性
     */
    private void validateParentIdUniqueness(PerformanceTargetTreeDTO target, Set<Long> parentIds) {
        if (target.getParentId() == null) {
            throw new BizCustomException(100, PerformanceValidationMessages.PARENT_ID_DUPLICATE);
        }
        if (parentIds.isEmpty()) {
            parentIds.add(target.getParentId());
            return;
        }
        if (parentIds.add(target.getParentId())) {
            throw new BizCustomException(100, PerformanceValidationMessages.PARENT_ID_DUPLICATE);
        }
    }

    /**
     * 校验不能有子节点
     */
    private void validateNoChildren(PerformanceTargetTreeDTO target) {
        if (target.getChildren() != null && !target.getChildren().isEmpty()) {
            throw new BizCustomException(100, PerformanceValidationMessages.CHILDREN_NOT_ALLOWED);
        }
    }

    /**
     * 校验目标唯一性
     */
    private void validateTargetUniqueness(PerformanceTargetTreeDTO target, Long planId, Set<String> unionKeys) {
        //修改、自定义不校验
        if (target.getId() != null || MetricDataTypeEnum.UNDEFINED.getCode() == target.getMetricDataType()) {
            return;
        }
        PerformanceTarget performanceTarget = performanceTargetMapper.selectOne(new LambdaQueryWrapper<PerformanceTarget>()
                .eq(PerformanceTarget::getPlanId, planId)
                .eq(PerformanceTarget::getTargetId, target.getTargetId())
                .eq(PerformanceTarget::getMetricCode, target.getMetricCode())
                .eq(PerformanceTarget::getIsDeleted, 0)
                .ne(PerformanceTarget::getMetricDataType, MetricDataTypeEnum.UNDEFINED.getCode())
        );

        if (performanceTarget != null) {
            throw new BizCustomException(100, PerformanceValidationMessages
                    .formatTargetDuplicateError(target.getMetricCode(), target.getMetricDataType()));
        }

        String unionKey = planId + "_" + target.getTargetId() + "_" + target.getMetricCode() + "_" + target.getMetricDataType();
        if (!unionKeys.add(unionKey)) {
            throw new BizCustomException(100, PerformanceValidationMessages
                    .formatTargetDuplicateError(target.getMetricCode(), target.getMetricDataType()));
        }
    }
}
