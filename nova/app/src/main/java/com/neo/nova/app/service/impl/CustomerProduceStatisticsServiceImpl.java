package com.neo.nova.app.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.helper.BatchHelper;
import com.neo.nova.app.service.CustomerProduceStatisticsService;
import com.neo.nova.app.service.CustomerService;
import com.neo.nova.app.service.GoodsInfoService;
import com.neo.nova.app.vo.CustomerQueryVO;
import com.neo.nova.app.vo.GoodsInfoQueryVO;
import com.neo.nova.domain.constants.DataConstants;
import com.neo.nova.domain.dto.CustomerStatisticsDetailDTO;
import com.neo.nova.domain.entity.*;
import com.neo.nova.domain.enums.MetricCodeIdEnum;
import com.neo.nova.domain.enums.OrderTypeEnum;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import com.neo.nova.domain.gateway.st_trd_customer_produce_month_infoRepository;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTreeTagQueryOption;
import com.neo.tagcenter.client.rpc.TreeTagReadService;
import com.neo.user.client.tenant.api.UserTenantService;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.neo.nova.domain.constants.DataConstants.*;

/**
 * 客户产品统计数据服务实现
 * 负责处理 st_trd_customer_produce_day_info 和 st_trd_customer_produce_month_info 表的新增/修改操作
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class CustomerProduceStatisticsServiceImpl implements CustomerProduceStatisticsService {

    @Resource
    private st_trd_customer_produce_day_infoRepository dayInfoRepository;
    @Resource
    private st_trd_customer_produce_month_infoRepository monthInfoRepository;
    @Resource
    private CustomerService customerService;
    @Resource
    private GoodsInfoService goodsInfoService;
    @Resource
    private TreeTagReadService treeTagReadService;
    @Resource
    private UserTenantService userTenantService;

    @Override
    public void clearUnReachableData(List<CustomerStatisticsDetailDTO> detailDTOS, Long tenantId) {
        if (CollectionUtils.isEmpty(detailDTOS) || tenantId == null) {
            return;
        }

        log.info("客户数据清理开始");

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();
        Set<Long> goodsIds = new HashSet<>();

        for (CustomerStatisticsDetailDTO detailDTO : detailDTOS) {
            dateSet.addAll(getAllDaysInMonth(detailDTO.getStartDate()));
            customerIds.add(detailDTO.getCustomerId());
            goodsIds.add(detailDTO.getGoodsId());
        }

        // 查询客户信息
        List<CustomerInfo> customers = customerService.search(CustomerQueryVO.builder().tenantId(tenantId).ids(customerIds).build());
        customers.stream().filter(Objects::nonNull).filter(customer -> filterCustomerNames.contains(customer.getName()))
                .map(CustomerInfo::getId).forEach(customerIds::remove);

        // 查询商品信息
        List<GoodsInfo> goodsList = goodsInfoService.search(GoodsInfoQueryVO.builder().tenantId(tenantId).ids(goodsIds).build());
        //筛选需要被统计的货品类目
        Set<Long> includeTypeIds = queryTagsForOrder(tenantId);
        goodsList.stream().filter(Objects::nonNull).filter(goods -> !includeTypeIds.contains(goods.getGoodsTypeId()))
                .map(GoodsInfo::getId).forEach(goodsIds::remove);

        goodsIds.add(SUMMARY_GOODS_ID);

        int total = 0;

        //清理旧数据
        {
            LambdaQueryWrapper<st_trd_customer_produce_day_info> dayCustomerWrapper = new LambdaQueryWrapper<>();
            dayCustomerWrapper.in(st_trd_customer_produce_day_info::getVisitDate, dateSet)
                    .eq(st_trd_customer_produce_day_info::getTenantId, tenantId)
                    .notIn(st_trd_customer_produce_day_info::getCustomerId, customerIds);
            List<st_trd_customer_produce_day_info> unReachableCustomerDaylist = dayInfoRepository.list(dayCustomerWrapper);
            Set<Long> ids = unReachableCustomerDaylist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_day_info::getId).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                total += ids.size();
                String customerNames = unReachableCustomerDaylist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_day_info::getCustomerName).distinct().collect(Collectors.joining(","));
                log.info("清理日表客户维度数据，清理无法触达的客户{}个，客户名称：{},", ids.size(), customerNames);
                dayInfoRepository.removeBatchByIds(ids);
            }
        }
        {
            LambdaQueryWrapper<st_trd_customer_produce_day_info> dayGoodsWrapper = new LambdaQueryWrapper<>();
            dayGoodsWrapper.in(st_trd_customer_produce_day_info::getVisitDate, dateSet)
                    .eq(st_trd_customer_produce_day_info::getTenantId, tenantId)
                    .notIn(st_trd_customer_produce_day_info::getProduceId, goodsIds);
            List<st_trd_customer_produce_day_info> unReachableGoodsDaylist = dayInfoRepository.list(dayGoodsWrapper);
            Set<Long> ids = unReachableGoodsDaylist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_day_info::getId).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                total += ids.size();
                String produceNames = unReachableGoodsDaylist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_day_info::getProduceName).distinct().collect(Collectors.joining(","));
                log.info("清理日表客户维度数据，清理无法触达的货品{}个，货品名称：{},", ids.size(), produceNames);
                dayInfoRepository.removeBatchByIds(ids);
            }
        }
        {
            LambdaQueryWrapper<st_trd_customer_produce_month_info> monthCustomerWrapper = new LambdaQueryWrapper<>();
            monthCustomerWrapper.in(st_trd_customer_produce_month_info::getVisitDate, dateSet)
                    .eq(st_trd_customer_produce_month_info::getTenantId, tenantId)
                    .notIn(st_trd_customer_produce_month_info::getCustomerId, customerIds);
            List<st_trd_customer_produce_month_info> unReachableCustomerMonthlist = monthInfoRepository.list(monthCustomerWrapper);
            Set<Long> ids = unReachableCustomerMonthlist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_month_info::getId).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                total += ids.size();
                String customerNames = unReachableCustomerMonthlist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_month_info::getCustomerName).distinct().collect(Collectors.joining(","));
                log.info("清理月表客户维度数据，清理无法触达的客户{}个，客户名称：{},", ids.size(), customerNames);
                monthInfoRepository.removeBatchByIds(ids);
            }
        }
        {
            LambdaQueryWrapper<st_trd_customer_produce_month_info> monthGoodsWrapper = new LambdaQueryWrapper<>();
            monthGoodsWrapper.in(st_trd_customer_produce_month_info::getVisitDate, dateSet)
                    .eq(st_trd_customer_produce_month_info::getTenantId, tenantId)
                    .notIn(st_trd_customer_produce_month_info::getProduceId, goodsIds);
            List<st_trd_customer_produce_month_info> unReachableGoodsMonthlist = monthInfoRepository.list(monthGoodsWrapper);
            Set<Long> ids = unReachableGoodsMonthlist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_month_info::getId).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                total += ids.size();
                String produceNames = unReachableGoodsMonthlist.stream().filter(Objects::nonNull).map(st_trd_customer_produce_month_info::getProduceName).distinct().collect(Collectors.joining(","));
                log.info("清理月表客户维度数据，清理无法触达的货品{}个，货品名称：{},", ids.size(), produceNames);
                monthInfoRepository.removeBatchByIds(ids);
            }
        }

        //清理仅剩合计的数据
        {
            QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("customerId", "count(*) as count")
                    .eq("tenantId", tenantId)
                    .in("visit_date", dateSet)
                    .groupBy("customerId")
                    .having("count(*) <= 1");
            List<Map<String, Object>> records = dayInfoRepository.listMaps(queryWrapper);
            Set<Long> ids = records.stream().filter(Objects::nonNull).map(record -> record.get("customerId")).map(String::valueOf).map(Long::valueOf).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                total += ids.size();
                log.info("清理日表客户维度数据，清理无效合计值{}个", ids.size());
                dayInfoRepository.removeBatchByIds(ids);
            }
        }
        {
            QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("customerId", "count(*) as count")
                    .eq("tenantId", tenantId)
                    .in("visit_date", dateSet)
                    .groupBy("customerId")
                    .having("count(*) <= 1");
            List<Map<String, Object>> records = monthInfoRepository.listMaps(queryWrapper);
            Set<Long> ids = records.stream().filter(Objects::nonNull).map(record -> record.get("customerId")).map(String::valueOf).map(Long::valueOf).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                total += ids.size();
                log.info("清理月表客户维度数据，清理无效合计值{}个,", ids.size());
                monthInfoRepository.removeBatchByIds(ids);
            }
        }

        log.info("客户数据清理完成，累计清理{}个", total);
    }

    @Override
    public void processAndSaveRecords(List<CustomerStatisticsDetailDTO> detailDTOS, Long tenantId) {
        if (CollectionUtils.isEmpty(detailDTOS) || tenantId == null) {
            log.error("参数不能为空, detailDTOS: {}, tenantId: {}", detailDTOS, tenantId);
            return;
        }

        // 获取所有客户ID
        List<Long> customerIds = detailDTOS.stream().map(CustomerStatisticsDetailDTO::getCustomerId).filter(Objects::nonNull).distinct().toList();
        // 查询客户信息
        List<CustomerInfo> customers = customerService.search(CustomerQueryVO.builder().tenantId(tenantId).ids(customerIds).build());
        Map<Long, CustomerInfo> customerMap = customers.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(CustomerInfo::getId, customer -> customer));

        // 获取所有商品ID
        List<Long> goodsIds = detailDTOS.stream().map(CustomerStatisticsDetailDTO::getGoodsId).filter(Objects::nonNull).distinct().toList();
        // 查询商品信息
        List<GoodsInfo> goodsList = goodsInfoService.search(GoodsInfoQueryVO.builder().tenantId(tenantId).ids(goodsIds).build());
        Map<Long, GoodsInfo> goodsMap = goodsList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(GoodsInfo::getId, goods -> goods, (existing, replacement) -> existing));

        // 获取所有业务员ID
        List<Long> salesIds = customers.stream().filter(customer -> customer != null && customer.getSalesId() != null)
                .map(CustomerInfo::getSalesId).distinct().toList();
        // 查询业务员信息
        Map<Long, TenantUserInfoDTO> salesMap = Optional.ofNullable(userTenantService.queryMapByUserIds(tenantId, salesIds)).map(SingleResponse::getData).orElse(new HashMap<>());

        //筛选需要被统计的货品类目
        Set<Long> includeTypeIds = queryTagsForOrder(tenantId);

        // 转换并保存到统计表
        Map<String, st_trd_customer_produce_day_info> dayInfoMap = Maps.newHashMap();
        Map<String, st_trd_customer_produce_month_info> monthInfoMap = Maps.newHashMap();

        for (CustomerStatisticsDetailDTO detailDTO : detailDTOS) {

            LocalDateTime startDate = LocalDateTimeUtil.parse(detailDTO.getStartDate(), "yyyy-MM-dd");
            LocalDateTime endDate = LocalDateTimeUtil.parse(detailDTO.getEndDate(), "yyyy-MM-dd");

            // 判断是存日表还是月表
            boolean isSameDay = startDate.equals(endDate);
            boolean isSameMonth = startDate.getYear() == endDate.getYear() &&
                    startDate.getMonthValue() == endDate.getMonthValue();

            if (!isSameDay && !isSameMonth) {
                log.error("数据时间范围不在同一天或同一月内,跳过处理: customerId={},goodsId={},startDate={},endDate={}",
                        detailDTO.getCustomerId(), detailDTO.getGoodsId(), detailDTO.getStartDate(), detailDTO.getEndDate());
                continue;
            }

            CustomerInfo customer = customerMap.get(detailDTO.getCustomerId());
            //客户没匹配到，过滤
            if (customer == null) {
                continue;
            }
            //特判过滤调拨类客户数据
            if (filterCustomerNames.contains(customer.getName())) {
                continue;
            }
            //特判过滤调拨类客户数据
            if (filterCustomerNamesFromServer1.contains(customer.getName())
                    && SqlServerSourceEnums.SQLSERVER1.getValue().equals(detailDTO.getOutType())) {
                continue;
            }

            //货品
            GoodsInfo goodsInfo = goodsMap.get(detailDTO.getGoodsId());
            //允许直接添加全店合计
            if (goodsInfo == null && !DataConstants.SUMMARY_GOODS_ID.equals(detailDTO.getGoodsId())) {
                continue;
            }
            if (goodsInfo != null && !includeTypeIds.contains(goodsInfo.getGoodsTypeId())) {
                continue;
            }

            //销售人员
            TenantUserInfoDTO salesUser = salesMap.get(customer.getSalesId());

            //缓存
            String recordKey = buildRecordKey(detailDTO.getStartDate(), detailDTO.getCustomerId(), detailDTO.getGoodsId());

            //处理数据
            if (isSameDay) {
                st_trd_customer_produce_day_info existDayInfo = dayInfoMap.getOrDefault(recordKey, createDayInfo(detailDTO, customer, goodsInfo, salesUser));
                aggregateStaticsDataToDayInfo(detailDTO, existDayInfo);
                dayInfoMap.put(recordKey, existDayInfo);
            } else {
                st_trd_customer_produce_month_info existMonthInfo = monthInfoMap.getOrDefault(recordKey, createMonthInfo(detailDTO, customer, goodsInfo, salesUser));
                aggregateStaticsDataToMonthInfo(detailDTO, existMonthInfo);
                monthInfoMap.put(recordKey, existMonthInfo);
            }
        }


        if (!dayInfoMap.isEmpty()) {
            log.info("汇总日数据完成，合计:{}条", dayInfoMap.size());
            saveDayInfoRecords(new ArrayList<>(dayInfoMap.values()), tenantId);
        }

        if (!monthInfoMap.isEmpty()) {
            log.info("汇总月数据完成，合计:{}条", monthInfoMap.size());
            saveMonthInfoRecords(new ArrayList<>(monthInfoMap.values()), tenantId, false);
        }
    }

    /**
     * 获取tagIds
     */
    private Set<Long> queryTagsForOrder(Long tenantId) {
        Set<Long> result = new HashSet<>();
        try {

            BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
            option.setIncludeDeleted(true);
            option.setIncludeDisable(true);
            option.setQueryChild(true);

            includeItemTypeTagNames.forEach(tagName -> {
                MultiResponse<TagLeafInfoDto> response = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.GOODS_CATEGORY.getCode(), tagName, option);
                if (response.isSuccess() && response.getData() != null) {
                    response.getData().stream().map(TagLeafInfoDto::getId).forEach(result::add);
                }
            });

            filterItemTypeTagNames.forEach(tagName -> {
                MultiResponse<TagLeafInfoDto> response = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.GOODS_CATEGORY.getCode(), tagName, option);
                if (response.isSuccess() && response.getData() != null) {
                    response.getData().stream().map(TagLeafInfoDto::getId).forEach(result::remove);
                }
            });

            return result;
        } catch (Exception e) {
            log.warn("获取标签失败,tenantId:{}", tenantId);
        }
        return new HashSet<>();
    }


    /**
     * 创建日统计数据（指定货品信息）
     */
    private st_trd_customer_produce_day_info createDayInfo(CustomerStatisticsDetailDTO detailDTO,
                                                           CustomerInfo customerInfo,
                                                           GoodsInfo goodsInfo,
                                                           TenantUserInfoDTO salesUser) {
        st_trd_customer_produce_day_info dayInfo = new st_trd_customer_produce_day_info();

        dayInfo.setTenantId(detailDTO.getTenantId());
        dayInfo.setProduceId(detailDTO.getGoodsId());
        dayInfo.setCustomerId(detailDTO.getCustomerId());

        // 从客户信息中填充额外字段
        dayInfo.setCustomerName(customerInfo.getName());
        dayInfo.setCustomerCode(customerInfo.getCode());
        dayInfo.setCustomerMnemoCode(customerInfo.getMnemoCode());
        dayInfo.setCustomerTypeId(customerInfo.getCustomerTypeId());
        dayInfo.setCustomerLevel(customerInfo.getLevel());
        dayInfo.setSalesRegionId(customerInfo.getCustomerAreaId());
        dayInfo.setAdminRegionId(customerInfo.getAdminRegionId());
        dayInfo.setSalesId(customerInfo.getSalesId());
        dayInfo.setChannelId(customerInfo.getChannel());
        dayInfo.setSupermarketAreaId(customerInfo.getSupermarketAreaId());

        // 从货品信息中填充额外字段
        if (goodsInfo != null) {
            dayInfo.setProduceName(goodsInfo.getName());
            dayInfo.setProduceCode(goodsInfo.getCode());
            dayInfo.setProduceTypeId(goodsInfo.getGoodsTypeId());
            dayInfo.setProduceOemFlag(goodsInfo.getOemFlag());
        }

        // 填充业务员信息
        if (salesUser != null) {
            dayInfo.setSalesCode(String.valueOf(salesUser.getUserId()));
            dayInfo.setSalesName(salesUser.getUnick());
            dayInfo.setDepartId(salesUser.getDeptId());
        }

        dayInfo.setVisitDate(detailDTO.getStartDate());
        dayInfo.setCreated(TimeUtils.getCurrentTime());
        dayInfo.setUpdated(TimeUtils.getCurrentTime());
        dayInfo.setIsDeleted(0);

        return dayInfo;
    }


    /**
     * 创建月统计数据（指定货品信息）
     */
    private st_trd_customer_produce_month_info createMonthInfo(CustomerStatisticsDetailDTO detailDTO,
                                                               CustomerInfo customerInfo,
                                                               GoodsInfo goodsInfo,
                                                               TenantUserInfoDTO salesUser) {
        st_trd_customer_produce_month_info monthInfo = new st_trd_customer_produce_month_info();

        monthInfo.setTenantId(detailDTO.getTenantId());
        monthInfo.setProduceId(detailDTO.getGoodsId());
        monthInfo.setCustomerId(detailDTO.getCustomerId());

        // 从客户信息中填充额外字段
        monthInfo.setCustomerName(customerInfo.getName());
        monthInfo.setCustomerCode(customerInfo.getCode());
        monthInfo.setCustomerMnemoCode(customerInfo.getMnemoCode());
        monthInfo.setCustomerTypeId(customerInfo.getCustomerTypeId());
        monthInfo.setCustomerLevel(customerInfo.getLevel());
        monthInfo.setSalesRegionId(customerInfo.getCustomerAreaId());
        monthInfo.setAdminRegionId(customerInfo.getAdminRegionId());
        monthInfo.setSalesId(customerInfo.getSalesId());
        monthInfo.setChannelId(customerInfo.getChannel());
        monthInfo.setSupermarketAreaId(customerInfo.getSupermarketAreaId());

        // 从货品信息中填充额外字段
        if (goodsInfo != null) {
            monthInfo.setProduceName(goodsInfo.getName());
            monthInfo.setProduceCode(goodsInfo.getCode());
            monthInfo.setProduceTypeId(goodsInfo.getGoodsTypeId());
            monthInfo.setProduceOemFlag(goodsInfo.getOemFlag());
        }

        // 填充业务员信息
        if (salesUser != null) {
            monthInfo.setSalesCode(String.valueOf(salesUser.getUserId()));
            monthInfo.setSalesName(salesUser.getUnick());
            monthInfo.setDepartId(salesUser.getDeptId());
        }

        monthInfo.setVisitDate(detailDTO.getStartDate().substring(0, 7)); // 2025-01
        monthInfo.setCreated(TimeUtils.getCurrentTime());
        monthInfo.setUpdated(TimeUtils.getCurrentTime());
        monthInfo.setIsDeleted(0);

        return monthInfo;
    }

    @Override
    public void saveDayInfoRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId) {
        if (dayInfoList == null || dayInfoList.isEmpty()) {
            log.debug("日统计数据列表为空，跳过保存");
            return;
        }

        Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> inputMap = Maps.newHashMap();
        for (st_trd_customer_produce_day_info inputDayInfo : dayInfoList) {
            inputMap.computeIfAbsent(inputDayInfo.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(inputDayInfo.getCustomerId(), k -> new HashMap<>())
                    .put(inputDayInfo.getProduceId(), inputDayInfo);
        }

        List<st_trd_customer_produce_day_info> recordsToInsert = new ArrayList<>();
        List<st_trd_customer_produce_day_info> recordsToUpdate = new ArrayList<>();

        // 批量查询所有可能存在的日统计记录
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> existingDayRecordsMap = batchQueryExistingDayRecords(dayInfoList, tenantId);

        for (Map.Entry<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> dataEntry : inputMap.entrySet()) {
            String visitDate = dataEntry.getKey();
            Map<Long, Map<Long, st_trd_customer_produce_day_info>> customerRecordMap = existingDayRecordsMap.getOrDefault(visitDate, Maps.newHashMap());
            for (Map.Entry<Long, Map<Long, st_trd_customer_produce_day_info>> customerEntry : dataEntry.getValue().entrySet()) {
                Long customerId = customerEntry.getKey();
                Map<Long, st_trd_customer_produce_day_info> goodsMap = customerEntry.getValue();
                Map<Long, st_trd_customer_produce_day_info> goodsRecordMap = customerRecordMap.getOrDefault(customerId, Maps.newHashMap());
                //如果传入了合计值，以传入为准
                boolean processedSummary = false;
                if (goodsMap.containsKey(SUMMARY_GOODS_ID)) {
                    insertOrUpdate(goodsMap.get(SUMMARY_GOODS_ID), goodsRecordMap.get(SUMMARY_GOODS_ID), recordsToInsert, recordsToUpdate);
                    processedSummary = true;
                }
                //处理单个数据
                for (Map.Entry<Long, st_trd_customer_produce_day_info> goodsEntry : goodsMap.entrySet()) {
                    Long goodsId = goodsEntry.getKey();
                    st_trd_customer_produce_day_info input = goodsEntry.getValue();
                    insertOrUpdate(input, goodsRecordMap.get(goodsId), recordsToInsert, recordsToUpdate);
                    //循环后goodsRecordMap数据会被写入覆盖
                    goodsRecordMap.put(goodsId, input);
                }
                //处理db里的合计值
                if (!processedSummary) {
                    //合计值
                    st_trd_customer_produce_day_info recordSummary = goodsRecordMap.get(SUMMARY_GOODS_ID);
                    goodsRecordMap.remove(SUMMARY_GOODS_ID);
                    //存在其他记录时更新
                    if (!goodsRecordMap.isEmpty()) {
                        st_trd_customer_produce_day_info newSummary = createDaySummary(goodsRecordMap.values());
                        insertOrUpdate(newSummary, recordSummary, recordsToInsert, recordsToUpdate);
                    }
                }

            }
        }

        // 使用多线程批量处理记录
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 批量插入新记录
        if (!recordsToInsert.isEmpty()) {
            log.info("开始多线程新增日统计数据，批量插入新记录：{}条", recordsToInsert.size());
            CompletableFuture<Void> insertFuture = BatchHelper.batchOperateDB(recordsToInsert, "插入", dayInfoRepository::saveBatch);
            futures.add(insertFuture);
        }

        // 批量更新现有记录
        if (!recordsToUpdate.isEmpty()) {
            log.info("开始多线程更新日统计数据，批量更新记录：{}条", recordsToUpdate.size());
            CompletableFuture<Void> updateFuture = BatchHelper.batchOperateDB(recordsToUpdate, "更新", dayInfoRepository::updateBatchById);
            futures.add(updateFuture);
        }

        // 等待所有任务完成
        if (!futures.isEmpty()) {
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("日统计数据多线程处理异常", e);
                throw new RuntimeException("数据库批量操作失败", e);
            }
        }

        log.info("日统计数据多线程处理完成，共处理：新增{}条，更新{}条",
                recordsToInsert.size(), recordsToUpdate.size());
        // 日表数据保存完成后，汇总到月表
        if (!dayInfoList.isEmpty()) {
            log.info("开始汇总日表数据到月表，日表记录数：{}", dayInfoList.size());
            aggregateDayToMonthRecords(dayInfoList, tenantId);
        }
    }

    private void insertOrUpdate(st_trd_customer_produce_day_info input, st_trd_customer_produce_day_info exist,
                                List<st_trd_customer_produce_day_info> recordsToInsert,
                                List<st_trd_customer_produce_day_info> recordsToUpdate) {
        if (exist == null) {
            // 创建新记录
            input.setUpdated(TimeUtils.getCurrentTime()); // 设置更新时间
            recordsToInsert.add(input);
        } else {
            // 数据比对：如果数据库里的数据和要更新的数据没有区别，那就不更新
            if (!isDayRecordDataEqual(exist, input)) {
                // 更新现有记录
                input.setId(exist.getId());
                input.setCreated(exist.getCreated()); // 保持原创建时间
                recordsToUpdate.add(input);
            }
        }
    }

    private void insertOrUpdate(st_trd_customer_produce_month_info input, st_trd_customer_produce_month_info exist,
                                List<st_trd_customer_produce_month_info> recordsToInsert,
                                List<st_trd_customer_produce_month_info> recordsToUpdate) {
        if (exist == null) {
            // 创建新记录
            input.setUpdated(TimeUtils.getCurrentTime()); // 设置更新时间
            recordsToInsert.add(input);
        } else {
            // 数据比对：如果数据库里的数据和要更新的数据没有区别，那就不更新
            if (!isMonthRecordDataEqual(exist, input)) {
                // 更新现有记录
                input.setId(exist.getId());
                input.setCreated(null); // 保持原创建时间
                recordsToUpdate.add(input);
            }
        }
    }

    @Override
    public void saveMonthInfoRecords(List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId, boolean fromDay) {
        if (monthInfoList == null || monthInfoList.isEmpty()) {
            log.debug("月统计数据列表为空，跳过保存");
            return;
        }

        Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> inputMap = Maps.newHashMap();
        for (st_trd_customer_produce_month_info inputMonthInfo : monthInfoList) {
            inputMap.computeIfAbsent(inputMonthInfo.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(inputMonthInfo.getCustomerId(), k -> new HashMap<>())
                    .put(inputMonthInfo.getProduceId(), inputMonthInfo);
        }

        List<st_trd_customer_produce_month_info> recordsToInsert = new ArrayList<>();
        List<st_trd_customer_produce_month_info> recordsToUpdate = new ArrayList<>();

        // 批量查询所有可能存在的日统计记录
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> existingMonthRecordsMap = batchQueryExistingMonthRecords(monthInfoList, tenantId);

        for (Map.Entry<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> dataEntry : inputMap.entrySet()) {
            String visitDate = dataEntry.getKey();
            Map<Long, Map<Long, st_trd_customer_produce_month_info>> customerRecordMap = existingMonthRecordsMap.getOrDefault(visitDate, Maps.newHashMap());
            for (Map.Entry<Long, Map<Long, st_trd_customer_produce_month_info>> customerEntry : dataEntry.getValue().entrySet()) {
                Long customerId = customerEntry.getKey();
                Map<Long, st_trd_customer_produce_month_info> goodsMap = customerEntry.getValue();
                Map<Long, st_trd_customer_produce_month_info> goodsRecordMap = customerRecordMap.getOrDefault(customerId, Maps.newHashMap());
                //如果传入了合计值，以传入为准
                boolean processedSummary = false;
                if (goodsMap.containsKey(SUMMARY_GOODS_ID)) {
                    //由日表上报的合计值，需要和数据库比较、防止用户指定合计值的情况
                    insertOrUpdate(goodsMap.get(SUMMARY_GOODS_ID), goodsRecordMap.get(SUMMARY_GOODS_ID), recordsToInsert, recordsToUpdate);
                    goodsMap.remove(SUMMARY_GOODS_ID);
                    processedSummary = true;
                }
                //处理单个数据
                for (Map.Entry<Long, st_trd_customer_produce_month_info> goodsEntry : goodsMap.entrySet()) {
                    Long goodsId = goodsEntry.getKey();
                    st_trd_customer_produce_month_info input = goodsEntry.getValue();
                    insertOrUpdate(input, goodsRecordMap.get(goodsId), recordsToInsert, recordsToUpdate);
                    //循环后goodsRecordMap数据会被写入覆盖
                    goodsRecordMap.put(goodsId, input);
                }
                //处理db里的合计值
                if (!processedSummary) {
                    //合计值
                    st_trd_customer_produce_month_info recordSummary = goodsRecordMap.get(SUMMARY_GOODS_ID);
                    goodsRecordMap.remove(SUMMARY_GOODS_ID);
                    //存在其他记录时更新
                    if (!goodsRecordMap.isEmpty()) {
                        st_trd_customer_produce_month_info newSummary = createMonthSummary(goodsRecordMap.values());
                        insertOrUpdate(newSummary, recordSummary, recordsToInsert, recordsToUpdate);
                    }
                }

            }
        }

        // 使用多线程批量处理记录
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 批量插入新记录
        if (!recordsToInsert.isEmpty()) {
            log.info("开始多线程新增月统计数据，批量插入新记录：{}条", recordsToInsert.size());
            CompletableFuture<Void> insertFuture = BatchHelper.batchOperateDB(recordsToInsert, "插入", monthInfoRepository::saveBatch);
            futures.add(insertFuture);
        }

        // 批量更新现有记录
        if (!recordsToUpdate.isEmpty()) {
            log.info("开始多线程更新月统计数据，批量更新记录：{}条", recordsToUpdate.size());
            CompletableFuture<Void> updateFuture = BatchHelper.batchOperateDB(recordsToUpdate, "更新", monthInfoRepository::updateBatchById);
            futures.add(updateFuture);
        }

        // 等待所有任务完成
        if (!futures.isEmpty()) {
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("月统计数据多线程处理异常", e);
                throw new RuntimeException("数据库批量操作失败", e);
            }
        }

        log.info("月统计数据多线程处理完成，共处理：新增{}条，更新{}条",
                recordsToInsert.size(), recordsToUpdate.size());
    }

    /**
     * 批量查询已存在的日统计记录
     */
    private Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> batchQueryExistingDayRecords(
            List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId) {

        if (dayInfoList.isEmpty()) {
            return new HashMap<>();
        }

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();

        for (st_trd_customer_produce_day_info data : dayInfoList) {
            customerIds.add(data.getCustomerId());
            dateSet.add(data.getVisitDate());
        }

        // 批量查询所有可能存在的记录
        LambdaQueryWrapper<st_trd_customer_produce_day_info> batchQuery = new LambdaQueryWrapper<>();
        batchQuery.eq(st_trd_customer_produce_day_info::getTenantId, tenantId)
                .in(st_trd_customer_produce_day_info::getCustomerId, customerIds)
                .in(st_trd_customer_produce_day_info::getVisitDate, dateSet)
                .eq(st_trd_customer_produce_day_info::getIsDeleted, 0);

        List<st_trd_customer_produce_day_info> existingRecords = dayInfoRepository.list(batchQuery);


        // 构建查询结果映射
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> recordsMap = new HashMap<>();
        for (st_trd_customer_produce_day_info record : existingRecords) {
            recordsMap.computeIfAbsent(record.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(record.getCustomerId(), k -> new HashMap<>())
                    .put(record.getProduceId(), record);
        }

        log.info("批量查询日统计记录完成，查询到 {} 条已存在记录", existingRecords.size());
        return recordsMap;
    }

    /**
     * 批量查询已存在的月统计记录
     */
    private Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> batchQueryExistingMonthRecords(
            List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId) {

        if (monthInfoList.isEmpty()) {
            return new HashMap<>();
        }

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();

        for (st_trd_customer_produce_month_info data : monthInfoList) {
            customerIds.add(data.getCustomerId());
            dateSet.add(data.getVisitDate());
        }

        // 批量查询所有可能存在的记录
        LambdaQueryWrapper<st_trd_customer_produce_month_info> batchQuery = new LambdaQueryWrapper<>();
        batchQuery.eq(st_trd_customer_produce_month_info::getTenantId, tenantId)
                .in(st_trd_customer_produce_month_info::getCustomerId, customerIds)
                .in(st_trd_customer_produce_month_info::getVisitDate, dateSet)
                .eq(st_trd_customer_produce_month_info::getIsDeleted, 0);

        List<st_trd_customer_produce_month_info> existingRecords = monthInfoRepository.list(batchQuery);

        // 构建查询结果映射
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> recordsMap = new HashMap<>();
        for (st_trd_customer_produce_month_info record : existingRecords) {
            recordsMap.computeIfAbsent(record.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(record.getCustomerId(), k -> new HashMap<>())
                    .put(record.getProduceId(), record);
        }

        log.info("批量查询月统计记录完成，查询到 {} 条已存在记录", existingRecords.size());
        return recordsMap;
    }


    /**
     * 比较两个日统计记录的数据是否相等
     *
     * @param existing  数据库中的现有记录
     * @param newRecord 要更新的新记录
     * @return true表示数据相等，false表示数据不同
     */
    private boolean isDayRecordDataEqual(st_trd_customer_produce_day_info existing, st_trd_customer_produce_day_info newRecord) {
        if (existing == null || newRecord == null) {
            return false;
        }

        // 比较核心业务字段
        return _isRecordDataEqual(newRecord.getBillQty(), existing.getBillQty()) &&
                _isRecordDataEqual(newRecord.getBillAmount(), existing.getBillAmount()) &&
                _isRecordDataEqual(newRecord.getDeliveryQty(), existing.getDeliveryQty()) &&
                _isRecordDataEqual(newRecord.getDeliveryAmount(), existing.getDeliveryAmount()) &&
                _isRecordDataEqual(newRecord.getCostAmount(), existing.getCostAmount()) &&
                _isRecordDataEqual(newRecord.getBackAmount(), existing.getBackAmount()) &&
                _isRecordDataEqual(newRecord.getSalesQty(), existing.getSalesQty()) &&
                _isRecordDataEqual(newRecord.getSalesAmount(), existing.getSalesAmount()) &&
                _isRecordDataEqual(newRecord.getSalesLossQty(), existing.getSalesLossQty()) &&
                _isRecordDataEqual(newRecord.getSalesLossAmount(), existing.getSalesLossAmount()) &&

                (newRecord.getProduceName() == null || Objects.equals(existing.getProduceName(), newRecord.getProduceName())) &&
                (newRecord.getProduceCode() == null || Objects.equals(existing.getProduceCode(), newRecord.getProduceCode())) &&
                (newRecord.getProduceTypeId() == null || Objects.equals(existing.getProduceTypeId(), newRecord.getProduceTypeId())) &&
                (newRecord.getProduceOemFlag() == null || Objects.equals(existing.getProduceOemFlag(), newRecord.getProduceOemFlag())) &&

                (newRecord.getCustomerName() == null || Objects.equals(existing.getCustomerName(), newRecord.getCustomerName())) &&
                (newRecord.getCustomerCode() == null || Objects.equals(existing.getCustomerCode(), newRecord.getCustomerCode())) &&
                (newRecord.getCustomerMnemoCode() == null || Objects.equals(existing.getCustomerMnemoCode(), newRecord.getCustomerMnemoCode())) &&
                (newRecord.getCustomerTypeId() == null || Objects.equals(existing.getCustomerTypeId(), newRecord.getCustomerTypeId())) &&
                (newRecord.getCustomerLevel() == null || Objects.equals(existing.getCustomerLevel(), newRecord.getCustomerLevel())) &&

                (newRecord.getSalesId() == null || Objects.equals(existing.getSalesId(), newRecord.getSalesId())) &&
                (newRecord.getSalesCode() == null || Objects.equals(existing.getSalesCode(), newRecord.getSalesCode())) &&
                (newRecord.getSalesName() == null || Objects.equals(existing.getSalesName(), newRecord.getSalesName())) &&
                (newRecord.getDepartId() == null || Objects.equals(existing.getDepartId(), newRecord.getDepartId())) &&

                (newRecord.getSalesRegionId() == null || Objects.equals(existing.getSalesRegionId(), newRecord.getSalesRegionId())) &&
                (newRecord.getAdminRegionId() == null || Objects.equals(existing.getAdminRegionId(), newRecord.getAdminRegionId())) &&
                (newRecord.getChannelId() == null || Objects.equals(existing.getChannelId(), newRecord.getChannelId())) &&
                (newRecord.getSupermarketAreaId() == null || Objects.equals(existing.getSupermarketAreaId(), newRecord.getSupermarketAreaId()));
    }


    private boolean _isRecordDataEqual(BigDecimal newRecord, BigDecimal exist) {
        //为空不更新
        if (newRecord == null) {
            return true;
        }
        if (exist == null) {
            return false;
        }
        return newRecord.setScale(5, RoundingMode.HALF_UP).compareTo(exist.setScale(5, RoundingMode.HALF_UP)) == 0;
    }

    /**
     * 比较两个月统计记录的数据是否相等
     *
     * @param existing  数据库中的现有记录
     * @param newRecord 要更新的新记录
     * @return true表示数据相等，false表示数据不同
     */
    private boolean isMonthRecordDataEqual(st_trd_customer_produce_month_info existing, st_trd_customer_produce_month_info newRecord) {
        if (existing == null || newRecord == null) {
            return false;
        }

        // 比较核心业务字段
        return _isRecordDataEqual(newRecord.getBillQty(), existing.getBillQty()) &&
                _isRecordDataEqual(newRecord.getBillAmount(), existing.getBillAmount()) &&
                _isRecordDataEqual(newRecord.getDeliveryQty(), existing.getDeliveryQty()) &&
                _isRecordDataEqual(newRecord.getDeliveryAmount(), existing.getDeliveryAmount()) &&
                _isRecordDataEqual(newRecord.getCostAmount(), existing.getCostAmount()) &&
                _isRecordDataEqual(newRecord.getBackAmount(), existing.getBackAmount()) &&
                _isRecordDataEqual(newRecord.getSalesQty(), existing.getSalesQty()) &&
                _isRecordDataEqual(newRecord.getSalesAmount(), existing.getSalesAmount()) &&
                _isRecordDataEqual(newRecord.getSalesLossQty(), existing.getSalesLossQty()) &&
                _isRecordDataEqual(newRecord.getSalesLossAmount(), existing.getSalesLossAmount()) &&

                (newRecord.getProduceName() == null || Objects.equals(existing.getProduceName(), newRecord.getProduceName())) &&
                (newRecord.getProduceCode() == null || Objects.equals(existing.getProduceCode(), newRecord.getProduceCode())) &&
                (newRecord.getProduceTypeId() == null || Objects.equals(existing.getProduceTypeId(), newRecord.getProduceTypeId())) &&
                (newRecord.getProduceOemFlag() == null || Objects.equals(existing.getProduceOemFlag(), newRecord.getProduceOemFlag())) &&

                (newRecord.getCustomerName() == null || Objects.equals(existing.getCustomerName(), newRecord.getCustomerName())) &&
                (newRecord.getCustomerCode() == null || Objects.equals(existing.getCustomerCode(), newRecord.getCustomerCode())) &&
                (newRecord.getCustomerMnemoCode() == null || Objects.equals(existing.getCustomerMnemoCode(), newRecord.getCustomerMnemoCode())) &&
                (newRecord.getCustomerTypeId() == null || Objects.equals(existing.getCustomerTypeId(), newRecord.getCustomerTypeId())) &&
                (newRecord.getCustomerLevel() == null || Objects.equals(existing.getCustomerLevel(), newRecord.getCustomerLevel())) &&

                (newRecord.getSalesId() == null || Objects.equals(existing.getSalesId(), newRecord.getSalesId())) &&
                (newRecord.getSalesCode() == null || Objects.equals(existing.getSalesCode(), newRecord.getSalesCode())) &&
                (newRecord.getSalesName() == null || Objects.equals(existing.getSalesName(), newRecord.getSalesName())) &&
                (newRecord.getDepartId() == null || Objects.equals(existing.getDepartId(), newRecord.getDepartId())) &&

                (newRecord.getSalesRegionId() == null || Objects.equals(existing.getSalesRegionId(), newRecord.getSalesRegionId())) &&
                (newRecord.getAdminRegionId() == null || Objects.equals(existing.getAdminRegionId(), newRecord.getAdminRegionId())) &&
                (newRecord.getChannelId() == null || Objects.equals(existing.getChannelId(), newRecord.getChannelId())) &&
                (newRecord.getSupermarketAreaId() == null || Objects.equals(existing.getSupermarketAreaId(), newRecord.getSupermarketAreaId()));
    }

    private st_trd_customer_produce_day_info createDaySummary(Collection<st_trd_customer_produce_day_info> allRecords) {
        st_trd_customer_produce_day_info summary = new st_trd_customer_produce_day_info();
        st_trd_customer_produce_day_info anyOne = allRecords.stream().findAny().get();
        BeanUtils.copyProperties(anyOne, summary);
        summary.setId(null);
        summary.setProduceId(SUMMARY_GOODS_ID);
        summary.setProduceName(SUMMARY_GOODS_NAME);
        summary.setProduceCode(null);
        summary.setProduceTypeId(null);
        // 汇总金额
        for (st_trd_customer_produce_day_info record : allRecords) {
            summary.setBillQty(addAmount(summary.getBillQty(), record.getBillQty()));
            summary.setBillAmount(addAmount(summary.getBillAmount(), record.getBillAmount()));
            summary.setDeliveryQty(addAmount(summary.getDeliveryQty(), record.getDeliveryQty()));
            summary.setDeliveryAmount(addAmount(summary.getDeliveryAmount(), record.getDeliveryAmount()));
            summary.setCostAmount(addAmount(summary.getCostAmount(), record.getCostAmount()));
            summary.setBackAmount(addAmount(summary.getBackAmount(), record.getBackAmount()));
            summary.setSalesQty(addAmount(summary.getSalesQty(), record.getSalesQty()));
            summary.setSalesAmount(addAmount(summary.getSalesAmount(), record.getSalesAmount()));
        }
        return summary;
    }

    private st_trd_customer_produce_month_info createMonthSummary(Collection<st_trd_customer_produce_month_info> allRecords) {
        st_trd_customer_produce_month_info summary = new st_trd_customer_produce_month_info();
        st_trd_customer_produce_month_info anyOne = allRecords.stream().findAny().get();
        BeanUtils.copyProperties(anyOne, summary);
        summary.setId(null);
        summary.setProduceId(SUMMARY_GOODS_ID);
        summary.setProduceName(SUMMARY_GOODS_NAME);
        summary.setProduceCode(null);
        summary.setProduceTypeId(null);
        for (st_trd_customer_produce_month_info record : allRecords) {
            summary.setBillQty(addAmount(summary.getBillQty(), record.getBillQty()));
            summary.setBillAmount(addAmount(summary.getBillAmount(), record.getBillAmount()));
            summary.setDeliveryQty(addAmount(summary.getDeliveryQty(), record.getDeliveryQty()));
            summary.setDeliveryAmount(addAmount(summary.getDeliveryAmount(), record.getDeliveryAmount()));
            summary.setCostAmount(addAmount(summary.getCostAmount(), record.getCostAmount()));
            summary.setBackAmount(addAmount(summary.getBackAmount(), record.getBackAmount()));
            summary.setSalesQty(addAmount(summary.getSalesQty(), record.getSalesQty()));
            summary.setSalesAmount(addAmount(summary.getSalesAmount(), record.getSalesAmount()));
        }
        return summary;
    }

    public BigDecimal addAmount(BigDecimal before, BigDecimal value) {
        if (value == null) {
            return before;
        }
        if (before == null) {
            return value;
        }
        return before.add(value);
    }

    @Override
    public void aggregateDayToMonthRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId) {
        if (dayInfoList == null || dayInfoList.isEmpty()) {
            log.debug("日统计数据列表为空，跳过汇总到月表");
            return;
        }

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();

        for (st_trd_customer_produce_day_info data : dayInfoList) {
            customerIds.add(data.getCustomerId());
            dateSet.addAll(getAllDaysInMonth(data.getVisitDate()));
        }

        // 批量查询所有可能存在的记录
        LambdaQueryWrapper<st_trd_customer_produce_day_info> batchQuery = new LambdaQueryWrapper<>();
        batchQuery.eq(st_trd_customer_produce_day_info::getTenantId, tenantId)
                .in(st_trd_customer_produce_day_info::getCustomerId, customerIds)
                .in(st_trd_customer_produce_day_info::getVisitDate, dateSet)
                .eq(st_trd_customer_produce_day_info::getIsDeleted, 0);

        List<st_trd_customer_produce_day_info> existingRecords = dayInfoRepository.list(batchQuery);

        // 构建查询结果映射
        Map<String, st_trd_customer_produce_month_info> recordsMap = new HashMap<>();
        for (st_trd_customer_produce_day_info record : existingRecords) {
            String monthDate = convertDayToMonthDate(record.getVisitDate());
            String key = buildRecordKey(monthDate, record.getCustomerId(), record.getProduceId());
            st_trd_customer_produce_month_info monthInfo = recordsMap.get(key);
            if (monthInfo == null) {
                recordsMap.put(key, convertDayInfoToMonthInfo(record, monthDate));
            } else {
                aggregateDayDataToMonthInfo(record, monthInfo);
            }
        }

        log.info("汇总月份的数据到月表，记录数：{}", recordsMap.size());

        // 按月份分组汇总日表数据
        saveMonthInfoRecords(new ArrayList<>(recordsMap.values()), tenantId, true);

        log.info("日表数据汇总到月表完成，共处理 {} 个数据", recordsMap.size());
    }

    private List<String> getAllDaysInMonth(String visitDate) {
        // 定义日期格式
        LocalDateTime localDateTime = LocalDateTimeUtil.parse(visitDate, "yyyy-MM-dd");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        int dayOfMonth = localDateTime.getMonth().length(localDateTime.getYear() % 4 == 0);
        List<String> result = Lists.newArrayList();
        // 循环输出每一天的日期
        for (int day = 1; day <= dayOfMonth; day++) {
            result.add(localDateTime.withDayOfMonth(day).format(formatter));
        }
        return result;
    }

    private String buildRecordKey(String visitDate, Long customerId, Long produceId) {
        return visitDate + "_" + customerId + "_" + produceId;
    }

    /**
     * 将日期从 "2025-01-01" 格式转换为 "2025-01" 格式
     */
    private String convertDayToMonthDate(String dayDate) {
        if (dayDate == null || dayDate.length() < 7) {
            return dayDate;
        }
        return dayDate.substring(0, 7); // 取前7位：2025-01
    }

    /**
     * 将日表实体转换为月表实体
     */
    private st_trd_customer_produce_month_info convertDayInfoToMonthInfo(st_trd_customer_produce_day_info dayInfo, String monthDate) {
        st_trd_customer_produce_month_info monthInfo = new st_trd_customer_produce_month_info();

        // 复制基本属性
        BeanUtils.copyProperties(dayInfo, monthInfo);

        // 重置ID和时间字段
        monthInfo.setId(null);
        monthInfo.setVisitDate(monthDate);
        monthInfo.setCreated(TimeUtils.getCurrentTime());
        monthInfo.setUpdated(TimeUtils.getCurrentTime());
        monthInfo.setExtra(null);
        return monthInfo;
    }

    /**
     * 将同一客户同一产品的多条日表数据汇总为一条月表数据
     */
    private void aggregateDayDataToMonthInfo(st_trd_customer_produce_day_info dayInfo, st_trd_customer_produce_month_info monthInfo) {
        monthInfo.setBillQty(_aggregateData(dayInfo.getBillQty(), monthInfo.getBillQty(), 1));
        monthInfo.setBillAmount(_aggregateData(dayInfo.getBillAmount(), monthInfo.getBillAmount(), 1));
        monthInfo.setDeliveryQty(_aggregateData(dayInfo.getDeliveryQty(), monthInfo.getDeliveryQty(), 1));
        monthInfo.setDeliveryAmount(_aggregateData(dayInfo.getDeliveryAmount(), monthInfo.getDeliveryAmount(), 1));
        monthInfo.setCostAmount(_aggregateData(dayInfo.getCostAmount(), monthInfo.getCostAmount(), 1));
        monthInfo.setBackAmount(_aggregateData(dayInfo.getBackAmount(), monthInfo.getBackAmount(), 1));
        monthInfo.setSalesQty(_aggregateData(dayInfo.getSalesQty(), monthInfo.getSalesQty(), 1));
        monthInfo.setSalesAmount(_aggregateData(dayInfo.getSalesAmount(), monthInfo.getSalesAmount(), 1));
        monthInfo.setSalesLossQty(_aggregateData(dayInfo.getSalesLossQty(), monthInfo.getSalesLossQty(), 1));
        monthInfo.setSalesLossAmount(_aggregateData(dayInfo.getSalesLossAmount(), monthInfo.getSalesLossAmount(), 1));
    }


    /**
     * 汇总日销量
     */
    private void aggregateStaticsDataToDayInfo(CustomerStatisticsDetailDTO detailDTO, st_trd_customer_produce_day_info dayInfo) {
        dayInfo.setBillQty(_aggregateData(detailDTO.getBillQty(), dayInfo.getBillQty(), detailDTO.getOrderType()));
        dayInfo.setBillAmount(_aggregateData(detailDTO.getBillAmount(), dayInfo.getBillAmount(), detailDTO.getOrderType()));
        dayInfo.setDeliveryQty(_aggregateData(detailDTO.getDeliveryQty(), dayInfo.getDeliveryQty(), detailDTO.getOrderType()));
        dayInfo.setDeliveryAmount(_aggregateData(detailDTO.getDeliveryAmount(), dayInfo.getDeliveryAmount(), detailDTO.getOrderType()));
        dayInfo.setCostAmount(_aggregateData(detailDTO.getCostAmount(), dayInfo.getCostAmount(), detailDTO.getOrderType()));
        dayInfo.setBackAmount(_aggregateData(detailDTO.getBackAmount(), dayInfo.getBackAmount(), detailDTO.getOrderType()));
        dayInfo.setSalesQty(_aggregateData(detailDTO.getSalesQty(), dayInfo.getSalesQty(), detailDTO.getOrderType()));
        dayInfo.setSalesAmount(_aggregateData(detailDTO.getSalesAmount(), dayInfo.getSalesAmount(), detailDTO.getOrderType()));

        //非商超，发货额就是销售额
        if (!MetricCodeIdEnum.CHANNEL_MARKET.getCode().equals(dayInfo.getChannelId())) {
            dayInfo.setSalesQty(dayInfo.getDeliveryQty());
            dayInfo.setSalesAmount(dayInfo.getDeliveryAmount());
        }

        //商超&退货&就地销毁计入损耗
        if (MetricCodeIdEnum.CHANNEL_MARKET.getCode().equals(dayInfo.getChannelId())
                && (OrderTypeEnum.REFUND_ORDER.getCode() == detailDTO.getOrderType())
                && detailDTO.getReturnProcess() != null && detailDTO.getReturnProcess().contains("就地销毁")) {
            dayInfo.setSalesLossQty(dayInfo.getDeliveryQty());
            dayInfo.setSalesLossAmount(dayInfo.getDeliveryAmount());
        }


        //外采产品取备用金额计入成本价
        if (dayInfo.getProduceOemFlag() != null && dayInfo.getProduceId() == 1) {
            dayInfo.setCostAmount(dayInfo.getBackAmount());
        }

    }

    /**
     * 汇总月销量
     */
    private void aggregateStaticsDataToMonthInfo(CustomerStatisticsDetailDTO detailDTO, st_trd_customer_produce_month_info monthInfo) {
        monthInfo.setBillQty(_aggregateData(detailDTO.getBillQty(), monthInfo.getBillQty(), detailDTO.getOrderType()));
        monthInfo.setBillAmount(_aggregateData(detailDTO.getBillAmount(), monthInfo.getBillAmount(), detailDTO.getOrderType()));
        monthInfo.setDeliveryQty(_aggregateData(detailDTO.getDeliveryQty(), monthInfo.getDeliveryQty(), detailDTO.getOrderType()));
        monthInfo.setDeliveryAmount(_aggregateData(detailDTO.getDeliveryAmount(), monthInfo.getDeliveryAmount(), detailDTO.getOrderType()));
        monthInfo.setCostAmount(_aggregateData(detailDTO.getCostAmount(), monthInfo.getCostAmount(), detailDTO.getOrderType()));
        monthInfo.setBackAmount(_aggregateData(detailDTO.getBackAmount(), monthInfo.getBackAmount(), detailDTO.getOrderType()));
        monthInfo.setSalesQty(_aggregateData(detailDTO.getSalesQty(), monthInfo.getSalesQty(), detailDTO.getOrderType()));
        monthInfo.setSalesAmount(_aggregateData(detailDTO.getSalesAmount(), monthInfo.getSalesAmount(), detailDTO.getOrderType()));

        //非商超，发货额就是销售额
        if (!MetricCodeIdEnum.CHANNEL_MARKET.getCode().equals(monthInfo.getChannelId())) {
            monthInfo.setSalesQty(monthInfo.getDeliveryQty());
            monthInfo.setSalesAmount(monthInfo.getDeliveryAmount());
        }

        //商超&退货&就地销毁计入损耗
        if (MetricCodeIdEnum.CHANNEL_MARKET.getCode().equals(monthInfo.getChannelId())
                && (OrderTypeEnum.REFUND_ORDER.getCode() == detailDTO.getOrderType())
                && detailDTO.getReturnProcess() != null && detailDTO.getReturnProcess().contains("就地销毁")) {
            monthInfo.setSalesLossQty(monthInfo.getDeliveryQty());
            monthInfo.setSalesLossAmount(monthInfo.getDeliveryAmount());
        }

        //外采产品取备用金额计入成本价
        if (monthInfo.getProduceOemFlag() != null && monthInfo.getProduceOemFlag() == 1) {
            monthInfo.setCostAmount(monthInfo.getBackAmount());
        }
    }

    private BigDecimal _aggregateData(BigDecimal newData, BigDecimal existData, Integer orderType) {
        if (newData == null) {
            newData = BigDecimal.ZERO;
        }
        if (OrderTypeEnum.FIX_ORDER.getCode() == orderType
                || OrderTypeEnum.REFUND_ORDER.getCode() == orderType) {
            //相减
            if (newData.compareTo(BigDecimal.ZERO) > 0) {
                newData = newData.multiply(BigDecimal.valueOf(-1));
            }
        }
        if (existData == null) {
            return newData;
        }
        return existData.add(newData);
    }
}
