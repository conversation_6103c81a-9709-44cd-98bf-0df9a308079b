package com.neo.nova.app.vo;

import com.google.common.collect.Maps;
import com.neo.api.PageReqDTO;
import com.neo.nova.domain.dto.TimeCondition;
import com.neo.nova.domain.enums.TargetDataTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/3
 **/
@Data
public class DataFormQueryVO extends PageReqDTO {

    /**
     * 查询参数
     * key @see com.neo.nova.domain.enums.MetricCodeEnum
     */
    private Map<String, List<String>> queryOptions = Maps.newHashMap();
    /**
     * 排序字段
     */
    private String sortBy;
    /**
     * 排序方式
     * asc desc
     */
    private String sort = "desc";
    /**
     * 时间参数
     */
    private TimeCondition timeCondition;

    /**
     * 目标数据类型列表（多选）
     * 用于指定要查询和展示的数据类型
     * 如果为空或null，则默认展示所有数据类型
     * @see TargetDataTypeEnum
     */
    private List<String> targetDataTypes;

    private Integer isTimeed;

    /**
     * 前端不传，后端赋值
     */
    private Long tenantId;
}
