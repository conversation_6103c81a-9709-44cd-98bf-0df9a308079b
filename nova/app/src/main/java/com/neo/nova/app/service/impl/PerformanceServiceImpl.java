package com.neo.nova.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.service.CustomerSalesService;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.PerformanceDisassemblyVO;
import com.neo.nova.domain.dto.*;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.request.PerformanceCalcRequest;
import com.neo.nova.app.request.PerformanceCreateRequest;
import com.neo.nova.app.request.PerformanceDeleteRequest;
import com.neo.nova.app.request.PerformanceListRequest;
import com.neo.nova.app.service.PerformanceService;
import com.neo.nova.app.validator.PerformanceCreateRequestValidator;
import com.neo.nova.app.vo.PerformanceListVO;
import com.neo.nova.domain.entity.*;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.MetricDataTypeEnum;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import com.neo.nova.domain.gateway.*;
import com.neo.user.client.tenant.api.DepartmentService;
import com.neo.user.client.tenant.dto.DepartmentInfoDTO;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/6/30
 **/
@Slf4j
@Service
public class PerformanceServiceImpl implements PerformanceService {
    @Autowired
    private PerformancePlanRepository performancePlanRepository;
    @Autowired
    private PerformanceTargetRepository performanceTargetRepository;
    @Autowired
    private PerformanceCreateRequestValidator performanceCreateRequestValidator;
    @Resource
    @Lazy
    private CustomerSalesService customerSalesService;
    @Autowired
    private UserService userService;
    @Autowired
    private DepartmentService departmentService;

    @Override
    public PerformanceListVO list(PerformanceListRequest request) {
        //构建返回对象
        PerformanceListVO result = new PerformanceListVO();

        // 初始化或检查计划信息，获取计划ID
        initOrCheckPlanInfo(request, result);
        //查db
        List<PerformanceTarget> targets = getAllTargets(request);
        // 转换为树形结构
        List<PerformanceTargetTreeDTO> roots = convertToTree(targets);
        //填充owner信息
        batchFillOwnerInfo(roots, request.getTenantId());
        result.setTargetTreeDto(roots);
        // 计算进度并填充百分比
        calculateProgress(request, result);

        return result;
    }


    private void initOrCheckPlanInfo(PerformanceListRequest request, PerformanceListVO result) {
        //如果没有传入计划ID，则自动设置计划ID
        if (request.getPlanId() == null) {
            autoSetPlanId(request, result);
            return;
        }

        //传入计划ID，则使用传入的ID
        LambdaQueryWrapper<PerformancePlan> planQueryWrapper = new LambdaQueryWrapper<>();
        planQueryWrapper.eq(PerformancePlan::getId, request.getPlanId());
        planQueryWrapper.eq(PerformancePlan::getTenantId, request.getTenantId());
        planQueryWrapper.eq(PerformancePlan::getIsDeleted, 0);
        PerformancePlan plan = performancePlanRepository.getOne(planQueryWrapper);
        if (plan == null) {
            throw new RuntimeException("planId不存在，请检查输入的计划ID是否有效: " + request.getPlanId());
        }
        request.setCurrentPlan(plan);
        result.setPlanId(plan.getId());
        result.setPeriodType(plan.getPeriodType());
    }

    private List<PerformanceTarget> getAllTargets(PerformanceListRequest request) {
        //查询kpi权限
        Integer isCompany = request.getTab();

        //如果有权限，公司目标查询,无权限,则个人目标查询
        List<PerformanceTarget> targets;
        if (isCompany == 0) {
            LambdaQueryWrapper<PerformanceTarget> selectCompanyPlanWrapper = new LambdaQueryWrapper<>();
            selectCompanyPlanWrapper.eq(PerformanceTarget::getPlanId, request.getPlanId()).eq(PerformanceTarget::getTenantId, request.getTenantId()).eq(PerformanceTarget::getIsDeleted, 0);
            targets = performanceTargetRepository.list(selectCompanyPlanWrapper);
        } else {
            // 个人目标查询：查询我的及其下的所有子KPI
            targets = getPersonalTargetsWithChildren(request);
        }
        return targets;
    }

    /**
     * 获取个人目标及其所有子KPI
     *
     * @param request 查询请求
     * @return 个人目标及其所有子KPI列表
     */
    private List<PerformanceTarget> getPersonalTargetsWithChildren(PerformanceListRequest request) {
        // 1. 首先查询当前用户的直接KPI
        LambdaQueryWrapper<PerformanceTarget> personalWrapper = new LambdaQueryWrapper<>();
        personalWrapper.eq(PerformanceTarget::getPlanId, request.getPlanId())
                .eq(PerformanceTarget::getTenantId, request.getTenantId())
                .eq(PerformanceTarget::getOwnerId, request.getUserId())
                .eq(PerformanceTarget::getIsDeleted, 0);
        List<PerformanceTarget> personalTargets = performanceTargetRepository.list(personalWrapper);

        // 2. 如果没有个人KPI，直接返回空列表
        if (CollectionUtil.isEmpty(personalTargets)) {
            return new ArrayList<>();
        }

        // 3. 收集所有子KPI的ID（从children字段解析）
        Set<Long> allChildrenIds = new HashSet<>();
        for (PerformanceTarget target : personalTargets) {
            if (target.getChildren() != null && !target.getChildren().trim().isEmpty()) {
                // 解析children字段（逗号分隔的ID）
                String[] childrenIdArray = target.getChildren().split(",");
                for (String childIdStr : childrenIdArray) {
                    try {
                        Long childId = Long.parseLong(childIdStr.trim());
                        allChildrenIds.add(childId);
                    } catch (NumberFormatException e) {
                        // 忽略无效的ID
                    }
                }
            }
        }

        // 4. 批量查询所有子KPI
        List<PerformanceTarget> childTargets = new ArrayList<>();
        if (!allChildrenIds.isEmpty()) {
            LambdaQueryWrapper<PerformanceTarget> childWrapper = new LambdaQueryWrapper<>();
            childWrapper.eq(PerformanceTarget::getPlanId, request.getPlanId())
                    .eq(PerformanceTarget::getTenantId, request.getTenantId())
                    .in(PerformanceTarget::getId, allChildrenIds)
                    .eq(PerformanceTarget::getIsDeleted, 0);
            childTargets = performanceTargetRepository.list(childWrapper);
        }

        // 5. 合并个人KPI和所有子KPI
        Set<Long> targetIds = new HashSet<>();
        List<PerformanceTarget> result = new ArrayList<>();

        // 添加个人KPI
        for (PerformanceTarget target : personalTargets) {
            if (targetIds.add(target.getId())) {
                result.add(target);
            }
        }

        // 添加所有子KPI
        for (PerformanceTarget target : childTargets) {
            if (targetIds.add(target.getId())) {
                result.add(target);
            }
        }

        return result;
    }

    /**
     * @return
     */
    private List<PerformanceTargetTreeDTO> convertToTree(List<PerformanceTarget> targets) {
        if (CollectionUtil.isEmpty(targets)) {
            return null;
        }

        List<PerformanceTargetTreeDTO> rootList = new ArrayList<>();
        // 使用Map快速查找节点
        Map<Long, PerformanceTargetTreeDTO> nodeMap = new HashMap<>();
        // 初始化所有节点
        for (PerformanceTarget target : targets) {
            PerformanceTargetTreeDTO node = new PerformanceTargetTreeDTO();
            node.setId(target.getId());
            node.setPlanId(target.getPlanId());
            node.setParentId(target.getParentId());
            node.setRootId(target.getRootId());
            node.setTargetName(target.getTargetName());
            node.setTargetId(target.getTargetId());
            node.setMetricCode(target.getMetricCode());
            node.setOwnerId(target.getOwnerId());
            node.setUnit(target.getUnit());
            node.setTargetValue(target.getTargetValue());
            node.setId(target.getId());
            node.setMetricDataType(target.getMetricDataType());
            nodeMap.put(node.getId(), node);

            if (node.getParentId() == 0) {
                rootList.add(node);
            }
        }
        //遍历所有非根节点并将填入父节点的子节点列表中
        for (PerformanceTarget target : targets) {
            if (target.getParentId() != 0 && nodeMap.containsKey(target.getParentId())) {
                PerformanceTargetTreeDTO parentNode = nodeMap.get(target.getParentId());
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                parentNode.getChildren().add(nodeMap.get(target.getId()));
            }
        }
        return rootList;
    }

    /**
     * 查询并赋值当前值
     *
     * @param result
     */
    private void calculateProgress(PerformanceListRequest request, PerformanceListVO result) {
        if (CollectionUtil.isEmpty(result.getTargetTreeDto()) || request.getCurrentPlan() == null) {
            return;
        }

        //同比
        List<PerformanceTargetTreeDTO> allList = result.getAllList();
        //其他指标处理
        List<PerformanceTargetTreeDTO> gmvList = allList.stream()
                .filter(target -> {
                    if (MetricDataTypeEnum.GMV.getCode() == target.getMetricDataType()) {
                        return true;
                    }
                    target.setActualValue(BigDecimal.ZERO);
                    target.setActualValueShow("-");
                    return false;
                })
                .toList();
        if (CollectionUtil.isEmpty(gmvList)) {
            return;
        }
        //timeCondition
        Integer periodType = request.getCurrentPlan().getPeriodType();
        String periodStart = request.getCurrentPlan().getPeriodStart();
        String periodEnd = request.getCurrentPlan().getPeriodEnd();
        TimeCondition nowTimeCondition = new TimeCondition(periodType, periodStart, periodEnd);
        // 批量查询优化：先收集所有节点，然后批量查询
        Map<String, BigDecimal> actualValueCache = calculateProgressWithBatch(request.getTenantId(), gmvList, nowTimeCondition);
        // 计算进度
        for (PerformanceTargetTreeDTO node : result.getTargetTreeDto()) {
            setActualValue(node, actualValueCache, MetricDataTypeEnum.isGMV(node.getMetricDataType()) ? node.getTargetValue() : null);
        }
        // 环比
        Map<String, BigDecimal> momHistoricalValue = calculateProgressWithBatch(request.getTenantId(), gmvList, nowTimeCondition.copy().initMom());
        for (PerformanceTargetTreeDTO node : gmvList) {
            setMomAndYoyValue(node, momHistoricalValue, 1);
        }
        //同比
        Map<String, BigDecimal> yoyHistoricalValue = calculateProgressWithBatch(request.getTenantId(), gmvList, nowTimeCondition.copy().initYoy());
        for (PerformanceTargetTreeDTO node : gmvList) {
            setMomAndYoyValue(node, yoyHistoricalValue, 2);
        }
    }

    /**
     * 查询并赋值当前值
     */
    private void calculateProgress(Long tenantId,
                                   PerformanceTarget parentTarget,
                                   List<PerformanceTargetTreeDTO> allList,
                                   PerformancePlan plan) {
        Integer periodType = plan.getPeriodType();
        String periodStart = plan.getPeriodStart();
        String periodEnd = plan.getPeriodEnd();
        TimeCondition nowTimeCondition = new TimeCondition(periodType, periodStart, periodEnd);
        // 批量查询优化：先收集所有节点，然后批量查询
        Map<String, BigDecimal> actualValueCache = calculateProgressWithBatch(tenantId, allList, nowTimeCondition);
        PerformanceTarget root = parentTarget;
        if (parentTarget.getParentId() != 0L) {
            try {
                root = performanceTargetRepository.getById(parentTarget.getParentId());
            } catch (Exception e) {
                throw new BizCustomException(400, "获取根节点失败");
            }
        }
        BigDecimal rootValue = root.getTargetValue();
        // 使用缓存数据计算进度
        allList.forEach(node -> setActualValue(node, actualValueCache, rootValue));

        // 环比
        Map<String, BigDecimal> momHistoricalValue = calculateProgressWithBatch(tenantId, allList, nowTimeCondition.copy().initMom());
        for (PerformanceTargetTreeDTO node : allList) {
            setMomAndYoyValue(node, momHistoricalValue, 1);
        }
        //同比
        Map<String, BigDecimal> yoyHistoricalValue = calculateProgressWithBatch(tenantId, allList, nowTimeCondition.copy().initYoy());
        for (PerformanceTargetTreeDTO node : allList) {
            setMomAndYoyValue(node, yoyHistoricalValue, 2);
        }
    }

    /**
     * 批量计算进度 - 优化版本
     *
     * @param targets
     */
    private Map<String, BigDecimal> calculateProgressWithBatch(Long tenantId,
                                                               List<PerformanceTargetTreeDTO> targets,
                                                               TimeCondition timeCondition) {

        Map<String, BigDecimal> resultMap = Maps.newHashMap();

        // 按 metricCode 分组，构建批量查询
        Map<String, List<PerformanceTargetTreeDTO>> groupedTargets = targets.stream()
                .filter(target -> target.getMetricCode() != null && target.getTargetId() != null)
                .collect(Collectors.groupingBy(PerformanceTargetTreeDTO::getMetricCode));

        for (Map.Entry<String, List<PerformanceTargetTreeDTO>> entry : groupedTargets.entrySet()) {
            String metricCode = entry.getKey();
            List<PerformanceTargetTreeDTO> targetList = entry.getValue();

            Map<String, BigDecimal> actualValueMap = customerSalesService.queryActualValue(tenantId, metricCode,
                    targetList.stream().map(PerformanceTargetTreeDTO::getTargetId).collect(Collectors.toList()),
                    timeCondition);


            for (PerformanceTargetTreeDTO performanceTargetTreeDTO : targetList) {
                BigDecimal actualValue;
                if (MetricCodeEnum.ALLCOMPANY.getCode().equals(metricCode)) {
                    actualValue = actualValueMap.getOrDefault(null, BigDecimal.ZERO);
                } else {
                    actualValue = actualValueMap.getOrDefault(performanceTargetTreeDTO.getTargetId(), BigDecimal.ZERO);
                }
                resultMap.put(generateCacheKey(performanceTargetTreeDTO), actualValue);
            }
        }

        return resultMap;
    }

    /**
     * 使用缓存数据计算子树进度
     *
     * @param node
     * @param actualValueCache
     * @param rootValue
     */
    private void setActualValue(PerformanceTargetTreeDTO node,
                                Map<String, BigDecimal> actualValueCache,
                                BigDecimal rootValue) {
        if (MetricDataTypeEnum.isGMV(node.getMetricDataType())) {
            String cacheKey = generateCacheKey(node);
            BigDecimal actualValue = actualValueCache.getOrDefault(cacheKey, BigDecimal.ZERO);
            node.setTargetPercentShare(BigDecimal.ZERO.compareTo(rootValue) == 0 ? BigDecimal.ZERO :
                    node.getTargetValue().divide(rootValue, 4, RoundingMode.HALF_UP).stripTrailingZeros());

            node.setActualValue(actualValue.setScale(0, RoundingMode.HALF_UP).stripTrailingZeros());
            node.setActualValueShow(actualValue.setScale(0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            node.setActualPercentShare(BigDecimal.ZERO.compareTo(node.getTargetValue()) == 0 ? BigDecimal.ZERO :
                    actualValue.divide(node.getTargetValue(), 4, RoundingMode.HALF_UP).stripTrailingZeros());
        }

        if (node.getChildren() != null) {
            for (PerformanceTargetTreeDTO child : node.getChildren()) {
                setActualValue(child, actualValueCache,
                        rootValue != null ? rootValue : MetricDataTypeEnum.isGMV(child.getMetricDataType()) ? child.getTargetValue() : null);
            }
        }
    }

    /**
     * 生成缓存键
     */
    private static String generateCacheKey(PerformanceTargetTreeDTO node) {
        return node.getMetricCode() + "_" + node.getTargetId();
    }

    /**
     * 生成缓存键
     */
    private static String generateCacheKey(String metricCode, String targetId) {
        return metricCode + "_" + targetId;
    }


    /**
     * 查询并赋值当前值
     */
    private Table calculateChannelProgress(Long tenantId,
                                           List<PerformanceTargetTreeDTO> allList,
                                           TimeCondition timeCondition, int type) {

        // 按 cacheKey 分组
        Map<String, PerformanceTargetTreeDTO> groupedTargets = Maps.newHashMap();
        LinkedHashSet<String> userIds = Sets.newLinkedHashSet();
        LinkedHashSet<String> departIds = Sets.newLinkedHashSet();
        for (PerformanceTargetTreeDTO target : allList) {
            if (MetricCodeEnum.USER.getCode().equals(target.getMetricCode())) {
                userIds.add(target.getTargetId());
            }
            if (MetricCodeEnum.DEPARTMENT.getCode().equals(target.getMetricCode())) {
                departIds.add(target.getTargetId());
            }
            groupedTargets.put(generateCacheKey(target), target);
        }

        //查询
        List<Map<String, Object>> userRecords = _userChannelRecords(tenantId, timeCondition, Lists.newArrayList(userIds));
        List<Map<String, Object>> departRecords = _departChannelRecords(tenantId, timeCondition, Lists.newArrayList(departIds));
        //同期全量
        BigDecimal companyTotal = customerSalesService.queryActualValue(tenantId, MetricCodeEnum.ALLCOMPANY.getCode(), null, timeCondition).get(null);
        if (companyTotal == null) companyTotal = BigDecimal.ZERO;


        //表头
        Table table = new Table();
        table.addToFirstColumn(Column.builder().key("ownerName").title("承接人").build())
                .addToFirstColumn(Column.builder().key("ownerValue").title("销售额").build())
                .addToFirstColumn(Column.builder().key("ownerPercentShare").title("占比").build()) //销售额占同期真实全量销售额的比例
                .addToFirstColumn(Column.builder().key(MetricCodeEnum.tableNameMapping(MetricCodeEnum.CHANNEL.getCode()))
                        .title(MetricCodeEnum.CHANNEL.getName()).build())
                .addToFirstColumn(Column.builder().key("channelValue").title("销售额").build())
                .addToFirstColumn(Column.builder().key("channelPercentShare").title("占比").build()); //渠道销售额占承接人销售额的比例

        //先处理部门数据
        _buildChannelTableRow(table, departRecords, MetricCodeEnum.DEPARTMENT.getCode(), new ArrayList<>(departIds), groupedTargets, companyTotal, type);
        //用户数据
        _buildChannelTableRow(table, userRecords, MetricCodeEnum.USER.getCode(), new ArrayList<>(userIds), groupedTargets, companyTotal, type);

        return table;

    }

    private List<Map<String, Object>> _userChannelRecords(Long tenantId, TimeCondition timeCondition, List<String> userIds) {
        DataFormQueryVO dataFormQueryVO = new DataFormQueryVO();
        dataFormQueryVO.setTenantId(tenantId);
        dataFormQueryVO.setPageSize(500);
        dataFormQueryVO.setTimeCondition(timeCondition);
        dataFormQueryVO.getQueryOptions().put(MetricCodeEnum.USER.getCode(), userIds);
        dataFormQueryVO.getQueryOptions().put(MetricCodeEnum.CHANNEL.getCode(), null);
        IPage<Map<String, Object>> iPage = customerSalesService.batchActualValue(dataFormQueryVO);
        return iPage.getRecords();
    }

    private List<Map<String, Object>> _departChannelRecords(Long tenantId, TimeCondition timeCondition, List<String> departIds) {
        DataFormQueryVO dataFormQueryVO = new DataFormQueryVO();
        dataFormQueryVO.setTenantId(tenantId);
        dataFormQueryVO.setPageSize(500);
        dataFormQueryVO.setTimeCondition(timeCondition);
        dataFormQueryVO.getQueryOptions().put(MetricCodeEnum.DEPARTMENT.getCode(), departIds);
        dataFormQueryVO.getQueryOptions().put(MetricCodeEnum.CHANNEL.getCode(), null);
        IPage<Map<String, Object>> iPage = customerSalesService.batchActualValue(dataFormQueryVO);
        return iPage.getRecords();
    }


    private void _buildChannelTableRow(Table table, List<Map<String, Object>> departRecords,
                                       String metricCode, List<String> targetIds,
                                       Map<String, PerformanceTargetTreeDTO> groupedTargets,
                                       BigDecimal companyTotal, int type) {
        if (departRecords.isEmpty()) {
            return;
        }
        for (String targetId : targetIds) {
            for (Map<String, Object> record : departRecords) {
                Boolean isThis = Optional.ofNullable(record.get(MetricCodeEnum.metricCodeMapping(metricCode)))
                        .map(String::valueOf)
                        .map(targetId::equals).orElse(false);
                PerformanceTargetTreeDTO performanceTargetTreeDTO = groupedTargets.get(generateCacheKey(metricCode, targetId));
                if (!isThis || performanceTargetTreeDTO == null) {
                    continue;
                }

                BigDecimal ownerActual = performanceTargetTreeDTO.getMomValue();//type == 1 环比
                if (type == 2) ownerActual = performanceTargetTreeDTO.getYoyValue();  //环比
                if (ownerActual == null) ownerActual = BigDecimal.ZERO; //默认为0

                table.addRecord(record, Lists.newArrayList("amount"));
                table.addToLastRow("ownerName", Cell.builder().value(record.get(MetricCodeEnum.tableNameMapping(metricCode))).build());
                table.addToLastRow("ownerValue", Cell.builder().value(ownerActual.stripTrailingZeros()).build());
                String ownerPercentShare = companyTotal.compareTo(BigDecimal.ZERO) > 0 ?
                        ownerActual.divide(companyTotal, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() : "-";
                table.addToLastRow("ownerPercentShare", Cell.builder().value(ownerPercentShare).build().setPercent());

                BigDecimal amount = Optional.ofNullable(record.get("amount")).map(String::valueOf).map(BigDecimal::new).orElse(BigDecimal.ZERO);
                table.addToLastRow("channelValue", Cell.builder().value(amount.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()).build());
                String channelPercentShare = ownerActual.compareTo(BigDecimal.ZERO) > 0 ?
                        amount.divide(ownerActual, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() : "-";
                table.addToLastRow("channelPercentShare", Cell.builder().value(channelPercentShare).build().setPercent());
            }
        }

        table.setPageable(false);
        table.setPageSize((long) table.getData().size());
        table.setTotal((long) table.getData().size());
        table.setTotalPage(1L);
        table.setCurrentPage(1L);
    }

    /**
     * 自动设置计划ID
     * 排序逻辑：
     * c）当前时间在计划时间区间内？
     * 是：优先取计划时间最短的，即 min（periodEnd-periodStart）
     * d）存在未来发生的计划？
     * 是：优先取开始时间最近的，如果有多个则取时间最长的，即 max（periodEnd-periodStart）
     * e）存在已经结束的计划?
     * 是：优先取结束时间最近的，如果有多个则取时间最长的，即 max（periodEnd-periodStart）
     *
     * @param request 请求参数
     * @return 计划ID
     */
    public void autoSetPlanId(PerformanceListRequest request, PerformanceListVO result) {
        List<PerformancePlan> plans = performancePlanRepository
                .list(new LambdaQueryWrapper<PerformancePlan>()
                        .eq(PerformancePlan::getTenantId, request.getTenantId())
                        .eq(PerformancePlan::getIsDeleted, 0)
                        .orderBy(true, false, PerformancePlan::getId)
                );

        if (CollectionUtils.isEmpty(plans)) {
            return;
        }

        // 获取当前时间（秒级时间戳）
        long now = System.currentTimeMillis() / 1000;

        plans.sort((l, r) -> {
            // 转换时间字符串为时间戳
            Long lStartTime = PeriodTypeEnum.convertStartTime(l.getPeriodType(), l.getPeriodStart());
            Long lEndTime = PeriodTypeEnum.convertEndTime(l.getPeriodType(), l.getPeriodEnd());
            Long rStartTime = PeriodTypeEnum.convertStartTime(r.getPeriodType(), r.getPeriodStart());
            Long rEndTime = PeriodTypeEnum.convertEndTime(r.getPeriodType(), r.getPeriodEnd());


            Integer lnow = lEndTime < now ? 1 : lStartTime > now ? -1 : 0;
            Integer rnow = rEndTime < now ? 1 : rStartTime > now ? -1 : 0;
            if (lnow.equals(rnow)) {
                if (lnow == 0) {
                    // 当前的，取周期最短的
                    return Long.compare(lEndTime - lStartTime, rEndTime - rStartTime);
                } else if (lnow == -1) {
                    // 未来的 ，优先取开始时间最近的，如果有多个则取时间最长的，即 max（periodEnd-periodStart）
                    return lStartTime.equals(rStartTime) ? Long.compare(rEndTime - rStartTime, lEndTime - lStartTime) : Long.compare(lStartTime, rStartTime);
                } else {
                    // 过去的 ，优先取结束时间最近的，如果有多个则取时间最长的，即 max（periodEnd-periodStart）
                    return lEndTime.equals(rEndTime) ? Long.compare(rEndTime - rStartTime, lEndTime - lStartTime) : Long.compare(rEndTime, lEndTime);
                }
            }
            return lnow < rnow ? -1 : 1;
        });

        result.setPlans(plans);
        PerformancePlan currentPlan = null;
        Long currentPeriodsTime = null;
        Long currentTimeInterval = null;
        PerformancePlan futurePlan = null;
        Long futurePeriodsTime = null;

        for (PerformancePlan plan : plans) {
            Long startTime = PeriodTypeEnum.convertStartTime(plan.getPeriodType(), plan.getPeriodStart());
            Long endTime = PeriodTypeEnum.convertEndTime(plan.getPeriodType(), plan.getPeriodEnd());
            long periodsTime = endTime - startTime;
            //生效中的
            if (startTime <= now && endTime >= now) {
                if (currentPlan == null) {
                    currentPlan = plan;
                    currentPeriodsTime = endTime - startTime;
                    continue;
                }
                if (periodsTime < currentPeriodsTime) {
                    currentPlan = plan;
                    currentPeriodsTime = periodsTime;
                }
            }
            long timeInterval = endTime > now ? endTime - now : now - startTime;
            if (futurePlan == null) {
                futurePlan = currentPlan;
                futurePeriodsTime = periodsTime;
                currentTimeInterval = timeInterval;
            }
            if (timeInterval < currentTimeInterval) {
                futurePlan = currentPlan;
                futurePeriodsTime = periodsTime;
                currentTimeInterval = timeInterval;
            }
            if (timeInterval == currentTimeInterval) {
                if (periodsTime > futurePeriodsTime) {
                    futurePlan = currentPlan;
                    futurePeriodsTime = periodsTime;
                }
            }

        }

        if (currentPlan == null) {
            currentPlan = futurePlan;
        }
        if (currentPlan != null) {
            request.setCurrentPlan(currentPlan);
            request.setPlanId(currentPlan.getId());
            result.setPlanId(currentPlan.getId());
            result.setPeriodType(currentPlan.getPeriodType());
        }
    }


    /**
     * 批量增加和删除业绩目标
     *
     * @param request
     * @return
     */
    @Override
    public Boolean batchAddOrModify(PerformanceCreateRequest request) {
        // 1. 使用专门校验器进行参数校验，并获取计划ID
        Long planId = performanceCreateRequestValidator.validateAndGetPlanId(request);

        // 2. 如果校验器返回null，说明需要创建新计划
        if (planId == null) {
            planId = createNewPlan(request);
        }

        // 3. 目标业务逻辑校验
        performanceCreateRequestValidator.validateTargetBusinessLogic(request.getTargets(), planId);

        //赋值全公司目标责任人
        initCompanyInfo(request);


        // 3. 执行新增/修改/删除操作（事务）
        performBatchOperation(planId, request);

        return true;
    }

    private void initCompanyInfo(PerformanceCreateRequest request) {
        List<PerformanceTargetTreeDTO> targets = request.getTargets();
        for (PerformanceTargetTreeDTO target : targets) {
            if (target.getTargetId().equals(MetricCodeEnum.ALLCOMPANY.getCode())) {
                target.setOwnerId(request.getUserId());
            }
        }
    }


    /**
     * 创建新的业绩计划
     *
     * @return 新创建的计划ID
     */
    private Long createNewPlan(PerformanceCreateRequest request) {
        PerformancePlanDTO plan = request.getPlan();
        // 查询是否存在对应计划
        QueryWrapper<PerformancePlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("periodType", plan.getPeriodType())
                .eq("periodStart", plan.getPeriodStart())
                .eq("periodEnd", plan.getPeriodEnd())
                .eq("tenantId", request.getTenantId())
                .eq("isDeleted", 0);
        PerformancePlan existingPlan = performancePlanRepository.getOne(queryWrapper);

        //如果存在则使用其ID
        if (existingPlan != null) {
            return existingPlan.getId();
        }

        // 新增计划
        PerformancePlan newPlan = new PerformancePlan();
        BeanUtils.copyProperties(plan, newPlan);
        newPlan.setIsDeleted(0); // 默认值，表示未删除
        newPlan.setTenantId(request.getTenantId()); // 假设默认租户ID为1，根据实际情况调整

        // 设置当前时间相关字段
        long currentTime = System.currentTimeMillis() / 1000;
        newPlan.setCreated((currentTime)); // 转换为秒
        newPlan.setUpdated((currentTime));

        // 创建者和更新者信息 - 需要从上下文中获取，这里假设为1
        newPlan.setCreatedBy(request.getUserId());
        newPlan.setUpdatedBy(request.getUserId());

        //插入计划
        performancePlanRepository.save(newPlan);
        return newPlan.getId();
    }

    /**
     * 执行新增/修改/删除操作（事务）
     *
     * @param planId
     * @param request
     */
    public void performBatchOperation(Long planId, PerformanceCreateRequest request) {
        List<PerformanceTargetTreeDTO> targets = request.getTargets();

        long now = System.currentTimeMillis() / 1000;

        //查询rootId
        Long rootId = request.getParentId();
        if (rootId != 0L) {
            PerformanceTarget parent = performanceTargetRepository.getById(rootId);
            if (parent == null) {
                throw new BizCustomException(100, "父目标不存在");
            }
            if (parent.getRootId() == 0L) {
                rootId = parent.getId();
            } else {
                rootId = parent.getRootId();
            }
        }

        //遍历targets,如果targetValue有多于2位小数，则保留两位
        for (PerformanceTargetTreeDTO target : targets) {
            if (target.getTargetValue() != null) {
                target.setTargetValue(target.getTargetValue().setScale(2, RoundingMode.HALF_UP));
            }
        }


        //  获取传入的id
        List<Long> incomingIds = targets.stream().map(PerformanceTargetTreeDTO::getId).filter(Objects::nonNull).toList();

        LambdaQueryWrapper<PerformanceTarget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceTarget::getPlanId, planId)
                .eq(PerformanceTarget::getParentId, request.getParentId())
                .eq(PerformanceTarget::getTenantId, request.getTenantId())
                .eq(PerformanceTarget::getIsDeleted, 0);
        //  查出当前 parentId 下的所有子节点
        List<PerformanceTarget> existingTargets = performanceTargetRepository.list(queryWrapper);


        List<Long> existingIds = existingTargets.stream()
                .filter(target -> !MetricCodeEnum.ALLCOMPANY.getCode().equals(target.getMetricCode()))
                .map(PerformanceTarget::getId).toList();

        // b) 差集 -> 要软删除的数据
        List<Long> toDelete = existingIds.stream().filter(id -> !incomingIds.contains(id)).collect(Collectors.toList());
        if (!toDelete.isEmpty()) {
            PerformanceDeleteRequest performanceDeleteRequest = new PerformanceDeleteRequest();
            performanceDeleteRequest.setPerformanceIds(toDelete);
            performanceDeleteRequest.setTenantId(request.getTenantId());
            performanceDeleteRequest.setUserId(request.getUserId());
            deleteTarget(performanceDeleteRequest);
        }

        // c) 更新已有 & 插入新目标
        List<PerformanceTarget> toInsert = new ArrayList<>();
        for (PerformanceTargetTreeDTO targetDto : targets) {
            if (targetDto.getId() != null) {
                PerformanceTarget target = new PerformanceTarget();
                BeanUtils.copyProperties(targetDto, target);
                target.setTargetValue(targetDto.getTargetValue());
                target.setUpdatedBy(request.getTenantId());
                target.setUpdated(now);
                performanceTargetRepository.getBaseMapper().updateById(target);
            } else {
                PerformanceTarget target = new PerformanceTarget();
                BeanUtils.copyProperties(targetDto, target);
                target.setTargetId(targetDto.getTargetId());
                target.setTargetValue(targetDto.getTargetValue());
                target.setPlanId(planId);
                target.setIsDeleted(0); // 显式设置为未删除状态
                target.setRootId(rootId);
                target.setCreatedBy(request.getUserId());
                target.setCreated(now);
                target.setUpdatedBy(request.getUserId());
                target.setUpdated(now);
                target.setTenantId(request.getTenantId());
                toInsert.add(target);
            }
        }

        if (!toInsert.isEmpty()) {
            performanceTargetRepository.saveBatch(toInsert);
        }

        // e) 递归更新父节点的 children 字段
        updateParentChildrenFields(planId, request.getParentId(), request.getUserId());
    }


    /**
     * 递归更新子树
     *
     * @param planId
     * @param parentId
     */
    private void updateParentChildrenFields(Long planId, Long parentId, Long userId) {


        // 2. 批量查询所有子节点
        List<PerformanceTarget> childrenList = performanceTargetRepository.list(new LambdaQueryWrapper<PerformanceTarget>()
                .eq(PerformanceTarget::getPlanId, planId)
                .eq(PerformanceTarget::getIsDeleted, 0)
                .eq(PerformanceTarget::getParentId, parentId)

        );

        // 3. 构建父节点ID->子节点ID列表的映射
        Map<Long, List<Long>> parentIdToChildIdsMap = new HashMap<>();
        for (PerformanceTarget child : childrenList) {
            Long childParent = child.getParentId() != null ? child.getParentId() : 0L;
            parentIdToChildIdsMap.computeIfAbsent(childParent, k -> new ArrayList<>()).add(child.getId());
        }

        // 5. 准备更新数据
        List<PerformanceTarget> parentsToUpdate = new ArrayList<>();
        long updatedTime = System.currentTimeMillis() / 1000; // 使用long存储时间戳

        for (Map.Entry<Long, List<Long>> entry : parentIdToChildIdsMap.entrySet()) {
            Long childParent = entry.getKey();
            List<Long> childIds = entry.getValue();

            // 生成标准化子节点ID字符串
            String newChildren = childIds.stream().map(String::valueOf).collect(Collectors.joining(","));

            // 查找对应的父节点
            PerformanceTarget parent = new PerformanceTarget();

            // 仅当children变化时更新
            parent.setId(childParent);
            parent.setChildren(newChildren);
            parent.setUpdated(updatedTime);
            parent.setUpdatedBy(userId);
            parentsToUpdate.add(parent);

        }

        // 7. 精准字段更新（避免全字段覆盖）
        for (PerformanceTarget parent : parentsToUpdate) {
            performanceTargetRepository.updateById(parent);
        }
    }

    /**
     * 批量试算业绩目标
     *
     * @param request
     * @return
     */
    @Override
    public PerformanceDisassemblyVO preCalcV2(PerformanceCalcRequest request) {
        PerformanceDisassemblyVO result = new PerformanceDisassemblyVO();

        List<PerformanceTargetTreeDTO> targets = request.getTargets();
        result.setTargets(targets);
        if (CollectionUtil.isEmpty(targets)) {
            return result;
        }

        // 预处理：批量填充空值
        batchFillOwnerInfo(targets, request.getTenantId());

        // 获取计划信息
        PerformancePlan plan = performancePlanRepository.getBaseMapper().selectById(request.getPlanId());
        if (plan == null) {
            throw new BizCustomException(100, "计划不存在: " + request.getPlanId());
        }
        //parentTarget
        PerformanceTarget parentTarget = performanceTargetRepository.getById(targets.get(0).getParentId());
        if (parentTarget == null) {
            throw new BizCustomException(100, "父级目标不存在: " + targets.get(0).getParentId());
        }

        batchFillTargetName(targets, parentTarget);

        List<PerformanceTargetTreeDTO> gmvTargets =
                targets.stream().filter(target -> MetricDataTypeEnum.GMV.getCode() == target.getMetricDataType()).toList();
        if (CollectionUtil.isEmpty(gmvTargets)) {
            return result;
        }

        // 计算进度
        calculateProgress(request.getTenantId(), parentTarget, gmvTargets, plan);

        //一键赋值
        initCalcType(request, gmvTargets, parentTarget);

        Integer periodType = plan.getPeriodType();
        String periodStart = plan.getPeriodStart();
        String periodEnd = plan.getPeriodEnd();
        TimeCondition nowTimeCondition = new TimeCondition(periodType, periodStart, periodEnd);
        //计算环比表格
        Table momTable = calculateChannelProgress(request.getTenantId(), gmvTargets, nowTimeCondition.copy().initMom(), 1);
        result.setMomTable(momTable);

        //计算同比表格
        Table yoyTable = calculateChannelProgress(request.getTenantId(), gmvTargets, nowTimeCondition.copy().initYoy(), 2);
        result.setYoyTable(yoyTable);


        return result;
    }

    private void initCalcType(PerformanceCalcRequest request, List<PerformanceTargetTreeDTO> gmvTargets, PerformanceTarget parentTarget) {
        if (request.getCalcType() == null) {
            return;
        }
        //targetValue赋值
        if (request.getCalcType() == 1) {
            //a）calc=1时 ，取parent的目标值，平均给每个值
            distributeTargetValueEvenly(gmvTargets, parentTarget);
        } else if (request.getCalcType() == 3) {
            gmvTargets.forEach(target -> target.setTargetValue(
                    target.getMomValue().multiply(BigDecimal.valueOf(request.getCoefficient())).setScale(2, RoundingMode.HALF_UP)
            ));

        } else if (request.getCalcType() == 2) {
            gmvTargets.forEach(target -> target.setTargetValue(
                    target.getYoyValue().multiply(BigDecimal.valueOf(request.getCoefficient())).setScale(2, RoundingMode.HALF_UP)
            ));
        }
    }


    /**
     * 批量填充目标信息 - 优化版本，避免递归查询
     *
     * @param targets  目标列表
     * @param tenantId 租户ID
     */
    private void batchFillOwnerInfo(List<PerformanceTargetTreeDTO> targets, Long tenantId) {
        if (CollectionUtil.isEmpty(targets)) {
            return;
        }

        // 收集所有需要查询的节点
        List<PerformanceTargetTreeDTO> allNodes = new ArrayList<>();
        for (PerformanceTargetTreeDTO target : targets) {
            collectAllNodes(target, allNodes);
        }

        // 分类收集需要查询的ID
        Set<Long> userIds = new HashSet<>();
        Set<Long> departmentIds = new HashSet<>();

        for (PerformanceTargetTreeDTO node : allNodes) {
            if (StringUtils.isBlank(node.getTargetId())) {
                continue;
            }
            if (MetricCodeEnum.DEPARTMENT.getCode().equals(node.getMetricCode())) {
                departmentIds.add(Long.valueOf(node.getTargetId()));
            } else if (MetricCodeEnum.USER.getCode().equals(node.getMetricCode())) {
                userIds.add(Long.valueOf(node.getTargetId()));
            } else if (MetricCodeEnum.ALLCOMPANY.getCode().equals(node.getMetricCode())) {
                if (node.getOwnerId() != null) userIds.add(node.getOwnerId());
            }
        }

        // 批量查询名称
        Map<Long, String> userNameMap = batchQueryUserNames(new ArrayList<>(userIds));
        Map<Long, DepartmentInfoDTO> departmentNameMap = batchQueryDepartment(new ArrayList<>(departmentIds), tenantId);

        // 填充结果
        for (PerformanceTargetTreeDTO node : allNodes) {
            if (StringUtils.isNotBlank(node.getTargetId())) {
                if (MetricCodeEnum.DEPARTMENT.getCode().equals(node.getMetricCode())) {
                    DepartmentInfoDTO departmentInfoDTO = departmentNameMap.get(Long.valueOf(node.getTargetId()));
                    if (departmentInfoDTO != null) {
                        //赋值ownerName
                        node.setOwnerName(departmentInfoDTO.getName());
                        //填充ownerId
                        if (node.getOwnerId() == null && departmentInfoDTO.getLeaderId() != null) {
                            node.setOwnerId(departmentInfoDTO.getLeaderId());
                        }
                    }
                } else if (MetricCodeEnum.USER.getCode().equals(node.getMetricCode())) {
                    String userName = userNameMap.get(Long.valueOf(node.getTargetId()));
                    //赋值ownerName
                    node.setOwnerName(userName);
                    //填充ownerId
                    if (node.getOwnerId() == null) {
                        node.setOwnerId(Long.valueOf(node.getTargetId()));
                    }
                } else if (MetricCodeEnum.ALLCOMPANY.getCode().equals(node.getMetricCode())) {
                    if (node.getOwnerId() != null) {
                        //先用创建人名来覆盖
                        String userName = userNameMap.get(node.getOwnerId());
                        //赋值ownerName
                        node.setOwnerName(userName);
                    }

                }
            }
        }
    }


    private void batchFillTargetName(List<PerformanceTargetTreeDTO> targets, PerformanceTarget parent) {
        for (PerformanceTargetTreeDTO target : targets) {
            if (target.getTargetName() == null) {
                target.setTargetName(parent.getTargetName());
            }
        }
    }

    /**
     * 批量查询用户名称
     *
     * @param userIds 用户ID列表
     * @return 用户ID -> 用户名称的映射
     */
    private Map<Long, String> batchQueryUserNames(List<Long> userIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtil.isEmpty(userIds)) {
            return result;
        }

        try {
            // 调用用户服务批量查询
            var response = userService.queryMapByUserIds(userIds);
            if (response.isSuccess() && response.getData() != null) {
                Map<Long, UserInfoDTO> userMap = response.getData();
                for (Map.Entry<Long, UserInfoDTO> entry : userMap.entrySet()) {
                    UserInfoDTO userInfo = entry.getValue();
                    String displayName = userInfo.getUnick() != null ? userInfo.getUnick() : userInfo.getUname();
                    result.put(entry.getKey(), displayName);
                }
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响主流程
            System.err.println("批量查询用户名称失败: " + e.getMessage());
        }

        return result;
    }


    /**
     * 批量查询部门信息
     *
     * @param departmentIds 部门ID列表
     * @param tenantId      租户id
     * @return 部门ID -> 部门对象
     */
    private Map<Long, DepartmentInfoDTO> batchQueryDepartment(List<Long> departmentIds, Long tenantId) {
        Map<Long, DepartmentInfoDTO> result = new HashMap<>();
        if (CollectionUtil.isEmpty(departmentIds)) {
            return result;
        }

        try {
            // 调用部门服务批量查询
            SingleResponse<Map<Long, DepartmentInfoDTO>> departmentResponse = departmentService.getDeptInfoMapByIds(tenantId, departmentIds);
            if (departmentResponse.isSuccess() && departmentResponse.getData() != null) {
                Map<Long, DepartmentInfoDTO> data = departmentResponse.getData();
                for (DepartmentInfoDTO departmentInfoDTO : data.values()) {
                    result.put(departmentInfoDTO.getDeptId(), departmentInfoDTO);
                }
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响主流程
            log.error("批量查询部门leader失败: ", e);
        }

        return result;
    }

    /**
     * 收集树中的所有节点
     *
     * @param node     当前节点
     * @param allNodes 收集所有节点的列表
     */
    private void collectAllNodes(PerformanceTargetTreeDTO node, List<PerformanceTargetTreeDTO> allNodes) {
        allNodes.add(node);
        List<PerformanceTargetTreeDTO> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            for (PerformanceTargetTreeDTO child : children) {
                collectAllNodes(child, allNodes);
            }
        }
    }

    /**
     * 对多棵根节点树进行目标值平均分配
     */
    public void distributeTargetValueEvenly(List<PerformanceTargetTreeDTO> rootNodes, PerformanceTarget parentTarget) {
        if (rootNodes == null || rootNodes.isEmpty()) {
            return;
        }
        BigDecimal parentTargetValue = parentTarget.getTargetValue();
        long size = rootNodes.size();

        for (PerformanceTargetTreeDTO node : rootNodes) {
            node.setTargetValue(parentTargetValue.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
            node.setTargetPercentShare(BigDecimal.ONE.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP));
        }
    }


    /**
     * 使用缓存数据计算环比/同比
     *
     * @param node
     * @param historicalValueCache
     * @param type
     */
    private void setMomAndYoyValue(PerformanceTargetTreeDTO node, Map<String, BigDecimal> historicalValueCache, Integer type) {
        String cacheKey = generateCacheKey(node);
        BigDecimal historicalValue = historicalValueCache.getOrDefault(cacheKey, BigDecimal.ZERO);
        BigDecimal currentTargetValue = node.getTargetValue() != null ? node.getTargetValue() : BigDecimal.ZERO;

        if (type == 1) { // 环比计算
            if (historicalValue == null || historicalValue.compareTo(BigDecimal.ZERO) <= 0) {
                // 上期无数据或为0时的处理
                node.setMomValue(BigDecimal.ZERO);
                node.setMomPercentShare(BigDecimal.ZERO);
            } else {
                // 先赋值上期实际值到momPercent（保持原始精度）
                node.setMomValue(historicalValue.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros());
                node.setMomPercentShare(currentTargetValue.divide(historicalValue, 4, RoundingMode.HALF_UP).stripTrailingZeros());
            }
        } else { // 同比计算
            if (historicalValue == null || historicalValue.compareTo(BigDecimal.ZERO) <= 0) {
                // 上期无数据或为0时的处理
                node.setYoyValue(BigDecimal.ZERO);
                node.setYoyPercentShare(BigDecimal.ZERO);
            } else {
                // 先赋值上期实际值到momPercent（保持原始精度）
                node.setYoyValue(historicalValue.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros());
                node.setYoyPercentShare(currentTargetValue.divide(historicalValue, 4, RoundingMode.HALF_UP).stripTrailingZeros());

            }
        }
    }


    /**
     * 删除计划及其所有目标
     *
     * @param performanceDeleteRequest 删除请求
     * @return 删除结果
     */
    @Override
    @Transactional
    public Boolean deletePlan(PerformanceDeleteRequest performanceDeleteRequest) {
        Long planId = performanceDeleteRequest.getPlanId();

        // 1. 参数校验
        if (planId == null) {
            throw new IllegalArgumentException("计划ID不能为空");
        }

        // 2. 校验计划是否存在
        PerformancePlan existingPlan = performancePlanRepository.getOne(new LambdaQueryWrapper<PerformancePlan>()
                .eq(PerformancePlan::getId, planId)
                .eq(PerformancePlan::getTenantId, performanceDeleteRequest.getTenantId())
                .eq(PerformancePlan::getIsDeleted, 0));
        if (existingPlan == null) {
            throw new BizCustomException(100, "计划不存在或已被删除: " + planId);
        }

        long now = System.currentTimeMillis() / 1000;

        // 3. 软删除计划
        LambdaUpdateWrapper<PerformancePlan> planUpdateWrapper = new LambdaUpdateWrapper<>();
        planUpdateWrapper.eq(PerformancePlan::getId, planId)
                .eq(PerformancePlan::getTenantId, performanceDeleteRequest.getTenantId())
                .set(PerformancePlan::getIsDeleted, 1)
                .set(PerformancePlan::getUpdated, now)
                .set(PerformancePlan::getUpdatedBy, performanceDeleteRequest.getUserId());
        boolean planUpdateResult = performancePlanRepository.update(null, planUpdateWrapper);

        // 4. 软删除该计划下的所有目标
        LambdaUpdateWrapper<PerformanceTarget> targetUpdateWrapper = new LambdaUpdateWrapper<>();
        targetUpdateWrapper.eq(PerformanceTarget::getPlanId, planId)
                .eq(PerformanceTarget::getTenantId, performanceDeleteRequest.getTenantId())
                .eq(PerformanceTarget::getIsDeleted, 0)
                .set(PerformanceTarget::getIsDeleted, 1)
                .set(PerformanceTarget::getUpdated, now)
                .set(PerformanceTarget::getUpdatedBy, performanceDeleteRequest.getUserId());
        boolean update = performanceTargetRepository.update(null, targetUpdateWrapper);

        return planUpdateResult && update;
    }

    @Override
    public Boolean deleteTarget(PerformanceDeleteRequest request) {
        List<Long> performanceIds = request.getPerformanceIds();

        // 1. 参数校验
        if (performanceIds == null || performanceIds.isEmpty()) {
            throw new IllegalArgumentException("目标ID不能为空");
        }

        List<PerformanceTarget> records = performanceTargetRepository.list(new LambdaQueryWrapper<PerformanceTarget>()
                .in(PerformanceTarget::getId, performanceIds)
                .eq(PerformanceTarget::getTenantId, request.getTenantId())
                .eq(PerformanceTarget::getIsDeleted, 0)
        );
        if (records == null || records.size() != performanceIds.size()) {
            throw new BizCustomException(100, "目标不存在");
        }

        // 收集所有涉及的计划ID
        Set<Long> affectedPlanIds = records.stream()
                .map(PerformanceTarget::getPlanId)
                .collect(Collectors.toSet());

        long now = TimeUtils.getCurrentTime();

        Set<Long> deletedIds = Sets.newHashSet();
        deletedIds.addAll(performanceIds);
        for (PerformanceTarget record : records) {
            if (StringUtils.isNotBlank(record.getChildren())) {
                Arrays.stream(record.getChildren().split(",")).map(Long::parseLong).forEach(deletedIds::add);
            }
        }

        // 2. 软删除目标
        LambdaUpdateWrapper<PerformanceTarget> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper
                .in(PerformanceTarget::getId, deletedIds)
                .eq(PerformanceTarget::getTenantId, request.getTenantId())
                .set(PerformanceTarget::getIsDeleted, 1)
                .set(PerformanceTarget::getUpdated, now)
                .set(PerformanceTarget::getUpdatedBy, request.getUserId());
        boolean updated = performanceTargetRepository.update(deleteWrapper);

        // 3. 检查并删除空计划
        if (updated) {
            deleteEmptyPlans(affectedPlanIds, request.getTenantId(), request.getUserId(), now);
        }

        return updated;
    }

    @Override
    public BigDecimal queryRootTargets(Long tenantId, TimeCondition timeCondition) {
        if (tenantId == null || timeCondition == null) {
            return BigDecimal.ZERO;
        }


        // 1. 查询计划
        LambdaQueryWrapper<PerformancePlan> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(PerformancePlan::getPeriodStart, timeCondition.getStartDate())
                .eq(PerformancePlan::getPeriodType, timeCondition.getPeriodType())
                .eq(PerformancePlan::getIsDeleted, 0)
                .eq(PerformancePlan::getTenantId, tenantId)
                .last("LIMIT 1");

        PerformancePlan plan = performancePlanRepository.getOne(planWrapper);
        if (plan == null) {
            return BigDecimal.ZERO;
        }

        //2.查询用户目标
        LambdaQueryWrapper<PerformanceTarget> targetWrapper = new LambdaQueryWrapper<>();
        targetWrapper.eq(PerformanceTarget::getPlanId, plan.getId())
                .in(PerformanceTarget::getParentId, 0L)
                .eq(PerformanceTarget::getMetricDataType, MetricDataTypeEnum.GMV.getCode())
                .eq(PerformanceTarget::getIsDeleted, 0)
                .eq(PerformanceTarget::getTenantId, tenantId);
        List<PerformanceTarget> list = performanceTargetRepository.list(targetWrapper);

        BigDecimal result = BigDecimal.ZERO;
        for (PerformanceTarget target : list) {
            if (target.getTargetValue() == null) {
                continue;
            }
            result = result.add(target.getTargetValue());
        }
        return result;
    }

    @Override
    public Map<Long, BigDecimal> queryUserTargets(Long tenantId, Collection<Long> userIds, TimeCondition timeCondition) {
        if (tenantId == null || userIds == null || userIds.isEmpty() || timeCondition == null) {
            return Maps.newHashMap();
        }

        Map<Long, BigDecimal> result = Maps.newHashMap();

        // 1. 查询计划
        LambdaQueryWrapper<PerformancePlan> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(PerformancePlan::getPeriodStart, timeCondition.getStartDate())
                .eq(PerformancePlan::getPeriodType, timeCondition.getPeriodType())
                .eq(PerformancePlan::getIsDeleted, 0)
                .eq(PerformancePlan::getTenantId, tenantId)
                .last("LIMIT 1");

        PerformancePlan plan = performancePlanRepository.getOne(planWrapper);
        if (plan == null) {
            return result;
        }

        //2.查询用户目标
        LambdaQueryWrapper<PerformanceTarget> targetWrapper = new LambdaQueryWrapper<>();
        targetWrapper.eq(PerformanceTarget::getPlanId, plan.getId())
                .in(PerformanceTarget::getOwnerId, userIds)
                .eq(PerformanceTarget::getMetricDataType, MetricDataTypeEnum.GMV.getCode())
                .eq(PerformanceTarget::getIsDeleted, 0)
                .eq(PerformanceTarget::getTenantId, tenantId);
        List<PerformanceTarget> list = performanceTargetRepository.list(targetWrapper);

        for (PerformanceTarget target : list) {
            if (target.getTargetValue() == null) {
                continue;
            }
            BigDecimal userTarget = result.getOrDefault(target.getOwnerId(), BigDecimal.ZERO);
            result.put(target.getOwnerId(), userTarget.add(target.getTargetValue()));
        }
        return result;
    }

    /**
     * 删除没有目标的空计划
     */
    private void deleteEmptyPlans(Set<Long> planIds, Long tenantId, Long userId, long now) {
        for (Long planId : planIds) {
            // 检查计划下是否还有未删除的目标
            long targetCount = performanceTargetRepository.count(new LambdaQueryWrapper<PerformanceTarget>()
                    .eq(PerformanceTarget::getPlanId, planId)
                    .eq(PerformanceTarget::getTenantId, tenantId)
                    .eq(PerformanceTarget::getIsDeleted, 0));

            // 如果没有目标了，软删除该计划
            if (targetCount == 0) {
                LambdaUpdateWrapper<PerformancePlan> planDeleteWrapper = new LambdaUpdateWrapper<>();
                planDeleteWrapper.eq(PerformancePlan::getId, planId)
                        .eq(PerformancePlan::getTenantId, tenantId)
                        .eq(PerformancePlan::getIsDeleted, 0)
                        .set(PerformancePlan::getIsDeleted, 1)
                        .set(PerformancePlan::getUpdated, now)
                        .set(PerformancePlan::getUpdatedBy, userId);
                performancePlanRepository.update(null, planDeleteWrapper);
            }
        }
    }
}
