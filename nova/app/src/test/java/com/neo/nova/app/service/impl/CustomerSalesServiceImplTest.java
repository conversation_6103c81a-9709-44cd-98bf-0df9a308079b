package com.neo.nova.app.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.nova.domain.dto.TimeCondition;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

/**
 * CustomerSalesServiceImpl测试类
 * 验证新的查询条件和数据计算逻辑
 */
@ExtendWith(MockitoExtension.class)
class CustomerSalesServiceImplTest {

    @InjectMocks
    private CustomerSalesServiceImpl customerSalesService;

    private DataFormQueryVO testQueryVO;

    @BeforeEach
    void setUp() {
        testQueryVO = new DataFormQueryVO();
        testQueryVO.setTenantId(1L);
        testQueryVO.setPageIndex(1);
        testQueryVO.setPageSize(10);
        
        // 设置查询条件
        Map<String, List<String>> queryOptions = Maps.newHashMap();
        queryOptions.put(MetricCodeEnum.CHANNEL.getCode(), Lists.newArrayList("JOINT_SUPERMARKET"));
        queryOptions.put(MetricCodeEnum.MARKET.getCode(), Lists.newArrayList("GANZHOU"));
        queryOptions.put(MetricCodeEnum.PRODUCT_SOURCE.getCode(), Lists.newArrayList("SELF"));
        testQueryVO.setQueryOptions(queryOptions);
        
        // 设置时间条件
        TimeCondition timeCondition = TimeCondition.builder()
                .periodType(PeriodTypeEnum.MONTH.getCode())
                .startDate("2025-01")
                .endDate("2025-01")
                .build();
        testQueryVO.setTimeCondition(timeCondition);
    }

    @Test
    void testMetricCodeMapping() {
        // 测试新增的枚举映射
        assertEquals("marketId", MetricCodeEnum.metricCodeMapping("C_MARKET"));
        assertEquals("produceOemFlag", MetricCodeEnum.metricCodeMapping("D_PRODUCT_SOURCE"));
        assertEquals("channelId", MetricCodeEnum.metricCodeMapping("C_CHANNEL"));
    }

    @Test
    void testTableNameMapping() {
        // 测试表名映射
        assertEquals("marketName", MetricCodeEnum.tableNameMapping("C_MARKET"));
        assertEquals("produceSourceName", MetricCodeEnum.tableNameMapping("D_PRODUCT_SOURCE"));
        assertEquals("channelName", MetricCodeEnum.tableNameMapping("C_CHANNEL"));
    }

    @Test
    void testGetBigDecimalValue() {
        // 测试BigDecimal值获取方法
        Map<String, Object> testMap = Maps.newHashMap();
        testMap.put("amount", new BigDecimal("100.50"));
        testMap.put("stringAmount", "200.75");
        testMap.put("nullValue", null);
        testMap.put("invalidValue", "invalid");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CustomerSalesServiceImpl.class.getDeclaredMethod("getBigDecimalValue", Map.class, String.class);
            method.setAccessible(true);

            BigDecimal result1 = (BigDecimal) method.invoke(customerSalesService, testMap, "amount");
            assertEquals(new BigDecimal("100.50"), result1);

            BigDecimal result2 = (BigDecimal) method.invoke(customerSalesService, testMap, "stringAmount");
            assertEquals(new BigDecimal("200.75"), result2);

            BigDecimal result3 = (BigDecimal) method.invoke(customerSalesService, testMap, "nullValue");
            assertEquals(BigDecimal.ZERO, result3);

            BigDecimal result4 = (BigDecimal) method.invoke(customerSalesService, testMap, "invalidValue");
            assertEquals(BigDecimal.ZERO, result4);

        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testCalculateRatio() {
        // 测试占比计算方法
        try {
            java.lang.reflect.Method method = CustomerSalesServiceImpl.class.getDeclaredMethod("calculateRatio", BigDecimal.class, BigDecimal.class);
            method.setAccessible(true);

            // 正常计算
            BigDecimal result1 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("25"), new BigDecimal("100"));
            assertEquals(new BigDecimal("25.0000"), result1);

            // 除数为0
            BigDecimal result2 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("25"), BigDecimal.ZERO);
            assertEquals(BigDecimal.ZERO, result2);

            // 除数为null
            BigDecimal result3 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("25"), null);
            assertEquals(BigDecimal.ZERO, result3);

        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testCalculateGrowthRate() {
        // 测试增长率计算方法
        try {
            java.lang.reflect.Method method = CustomerSalesServiceImpl.class.getDeclaredMethod("calculateGrowthRate", BigDecimal.class, BigDecimal.class);
            method.setAccessible(true);

            // 正常增长
            BigDecimal result1 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("120"), new BigDecimal("100"));
            assertEquals(new BigDecimal("20.0000"), result1);

            // 负增长
            BigDecimal result2 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("80"), new BigDecimal("100"));
            assertEquals(new BigDecimal("-20.0000"), result2);

            // 基数为0
            BigDecimal result3 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("100"), BigDecimal.ZERO);
            assertEquals(BigDecimal.ZERO, result3);

            // 基数为null
            BigDecimal result4 = (BigDecimal) method.invoke(customerSalesService, new BigDecimal("100"), null);
            assertEquals(BigDecimal.ZERO, result4);

        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildRecordKey() {
        // 测试记录键构建方法
        try {
            java.lang.reflect.Method method = CustomerSalesServiceImpl.class.getDeclaredMethod("buildRecordKey", Map.class, java.util.Set.class);
            method.setAccessible(true);

            Map<String, Object> record = Maps.newHashMap();
            record.put("channelId", "JOINT_SUPERMARKET");
            record.put("marketId", "GANZHOU");
            record.put("produceOemFlag", 0);

            java.util.Set<String> queryOptions = java.util.Set.of("C_CHANNEL", "C_MARKET", "D_PRODUCT_SOURCE");

            String result = (String) method.invoke(customerSalesService, record, queryOptions);
            
            // 验证键包含所有查询条件
            assertTrue(result.contains("channelId:JOINT_SUPERMARKET"));
            assertTrue(result.contains("marketId:GANZHOU"));
            assertTrue(result.contains("produceOemFlag:0"));

        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testQueryOptionsValidation() {
        // 测试查询条件验证
        assertNotNull(testQueryVO.getQueryOptions());
        assertTrue(testQueryVO.getQueryOptions().containsKey(MetricCodeEnum.CHANNEL.getCode()));
        assertTrue(testQueryVO.getQueryOptions().containsKey(MetricCodeEnum.MARKET.getCode()));
        assertTrue(testQueryVO.getQueryOptions().containsKey(MetricCodeEnum.PRODUCT_SOURCE.getCode()));
        
        // 验证时间条件
        assertNotNull(testQueryVO.getTimeCondition());
        assertEquals(PeriodTypeEnum.MONTH.getCode(), testQueryVO.getTimeCondition().getPeriodType());
        assertEquals("2025-01", testQueryVO.getTimeCondition().getStartDate());
        assertEquals("2025-01", testQueryVO.getTimeCondition().getEndDate());
    }

    @Test
    void testEnumValues() {
        // 测试新增的枚举值
        assertEquals("市场", MetricCodeEnum.MARKET.getName());
        assertEquals("产品来源", MetricCodeEnum.PRODUCT_SOURCE.getName());
        assertEquals("C_MARKET", MetricCodeEnum.MARKET.getCode());
        assertEquals("D_PRODUCT_SOURCE", MetricCodeEnum.PRODUCT_SOURCE.getCode());
    }
}
