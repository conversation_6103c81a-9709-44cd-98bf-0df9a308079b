package com.neo.nova.app.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.neo.nova.domain.dto.TimeCondition;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import com.neo.nova.domain.enums.TargetDataTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * DataFormQueryVO使用示例
 * 展示如何使用新的targetDataTypes多选功能
 */
public class DataFormQueryVOExample {

    public static void main(String[] args) {
        System.out.println("DataFormQueryVO使用示例");
        
        // 示例1：查询所有数据类型（默认行为）
        System.out.println("\n=== 示例1：查询所有数据类型 ===");
        DataFormQueryVO queryVO1 = createBaseQueryVO();
        // 不设置targetDataTypes，将显示所有数据类型
        printQueryInfo(queryVO1);
        
        // 示例2：只查询销售额相关数据
        System.out.println("\n=== 示例2：只查询销售额相关数据 ===");
        DataFormQueryVO queryVO2 = createBaseQueryVO();
        queryVO2.setTargetDataTypes(Lists.newArrayList(
            TargetDataTypeEnum.SALES_AMOUNT.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode()
        ));
        printQueryInfo(queryVO2);
        
        // 示例3：只查询毛利额和毛利率
        System.out.println("\n=== 示例3：只查询毛利额和毛利率 ===");
        DataFormQueryVO queryVO3 = createBaseQueryVO();
        queryVO3.setTargetDataTypes(Lists.newArrayList(
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode()
        ));
        printQueryInfo(queryVO3);
        
        // 示例4：查询所有占比数据
        System.out.println("\n=== 示例4：查询所有占比数据 ===");
        DataFormQueryVO queryVO4 = createBaseQueryVO();
        queryVO4.setTargetDataTypes(Lists.newArrayList(
            TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode()
        ));
        printQueryInfo(queryVO4);
        
        // 示例5：查询所有增长率数据
        System.out.println("\n=== 示例5：查询所有增长率数据 ===");
        DataFormQueryVO queryVO5 = createBaseQueryVO();
        queryVO5.setTargetDataTypes(Lists.newArrayList(
            TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
        ));
        printQueryInfo(queryVO5);
        
        // 示例6：自定义组合
        System.out.println("\n=== 示例6：自定义组合（销售额 + 毛利率 + 环比增长） ===");
        DataFormQueryVO queryVO6 = createBaseQueryVO();
        queryVO6.setTargetDataTypes(Lists.newArrayList(
            TargetDataTypeEnum.SALES_AMOUNT.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode(),
            TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
            TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode()
        ));
        printQueryInfo(queryVO6);
        
        System.out.println("\n=== 所有示例完成 ===");
    }
    
    /**
     * 创建基础查询对象
     */
    private static DataFormQueryVO createBaseQueryVO() {
        DataFormQueryVO queryVO = new DataFormQueryVO();
        
        // 设置基本分页参数
        queryVO.setPageIndex(1);
        queryVO.setPageSize(10);
        
        // 设置查询条件
        Map<String, List<String>> queryOptions = Maps.newHashMap();
        queryOptions.put(MetricCodeEnum.CHANNEL.getCode(), Lists.newArrayList("JOINT_SUPERMARKET"));
        queryOptions.put(MetricCodeEnum.MARKET.getCode(), Lists.newArrayList("GANZHOU"));
        queryVO.setQueryOptions(queryOptions);
        
        // 设置时间条件
        TimeCondition timeCondition = TimeCondition.builder()
                .periodType(PeriodTypeEnum.MONTH.getCode())
                .startDate("2025-01")
                .endDate("2025-01")
                .build();
        queryVO.setTimeCondition(timeCondition);
        
        // 设置租户ID
        queryVO.setTenantId(1L);
        
        return queryVO;
    }
    
    /**
     * 打印查询信息
     */
    private static void printQueryInfo(DataFormQueryVO queryVO) {
        System.out.println("查询条件：");
        System.out.println("  - 渠道：" + queryVO.getQueryOptions().get(MetricCodeEnum.CHANNEL.getCode()));
        System.out.println("  - 市场：" + queryVO.getQueryOptions().get(MetricCodeEnum.MARKET.getCode()));
        System.out.println("  - 时间：" + queryVO.getTimeCondition().getStartDate() + " 到 " + queryVO.getTimeCondition().getEndDate());
        
        List<String> targetDataTypes = queryVO.getTargetDataTypes();
        if (targetDataTypes == null || targetDataTypes.isEmpty()) {
            System.out.println("目标数据类型：全部（默认）");
        } else {
            System.out.println("目标数据类型：");
            for (String dataType : targetDataTypes) {
                String name = TargetDataTypeEnum.getNameByCode(dataType);
                System.out.println("  - " + dataType + " (" + name + ")");
            }
        }
    }
    
    /**
     * 验证targetDataTypes参数的有效性
     */
    public static boolean validateTargetDataTypes(List<String> targetDataTypes) {
        if (targetDataTypes == null || targetDataTypes.isEmpty()) {
            return true; // 空列表是有效的，表示查询所有类型
        }
        
        for (String dataType : targetDataTypes) {
            if (!TargetDataTypeEnum.isValidCode(dataType)) {
                System.out.println("无效的targetDataType: " + dataType);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取所有可用的数据类型代码
     */
    public static List<String> getAllAvailableDataTypes() {
        List<String> allTypes = Lists.newArrayList();
        for (TargetDataTypeEnum enumValue : TargetDataTypeEnum.values()) {
            allTypes.add(enumValue.getCode());
        }
        return allTypes;
    }
    
    /**
     * 根据分类获取数据类型
     */
    public static class DataTypeCategories {
        
        public static List<String> getSalesAmountTypes() {
            return Lists.newArrayList(
                TargetDataTypeEnum.SALES_AMOUNT.getCode(),
                TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
                TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getGrossProfitAmountTypes() {
            return Lists.newArrayList(
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getGrossProfitMarginTypes() {
            return Lists.newArrayList(
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
            );
        }
        
        public static List<String> getRatioTypes() {
            return Lists.newArrayList(
                TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode()
            );
        }
        
        public static List<String> getMomGrowthTypes() {
            return Lists.newArrayList(
                TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode()
            );
        }
        
        public static List<String> getYoyGrowthTypes() {
            return Lists.newArrayList(
                TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode()
            );
        }
    }
}
