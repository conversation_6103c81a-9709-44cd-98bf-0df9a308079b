<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.CustomerInfoMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.CustomerInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenantId" jdbcType="BIGINT"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="outId1" column="outId1" jdbcType="VARCHAR"/>
        <result property="outId2" column="outId2" jdbcType="VARCHAR"/>
        <result property="outType" column="outType" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="VARCHAR"/>
        <result property="supermarketAreaId" column="supermarketAreaId" jdbcType="VARCHAR"/>
        <result property="mnemoCode" column="mnemoCode" jdbcType="VARCHAR"/>
        <result property="linkMan" column="linkMan" jdbcType="VARCHAR"/>
        <result property="linkPosition" column="linkPosition" jdbcType="VARCHAR"/>
        <result property="contractNumber" column="contractNumber" jdbcType="VARCHAR"/>
        <result property="deliveryAddress" column="deliveryAddress" jdbcType="VARCHAR"/>
        <result property="customerTypeId" column="customerTypeId" jdbcType="BIGINT"/>
        <result property="customerLineId" column="customerLineId" jdbcType="BIGINT"/>
        <result property="adminRegionId" column="adminRegionId" jdbcType="BIGINT"/>
        <result property="customerAreaId" column="customerAreaId" jdbcType="BIGINT"/>
        <result property="openDate" column="openDate" jdbcType="BIGINT"/>
        <result property="priceTypeId" column="priceTypeId" jdbcType="BIGINT"/>
        <result property="auditFlag" column="auditFlag" jdbcType="TINYINT"/>
        <result property="level" column="level" jdbcType="TINYINT"/>
        <result property="salesId" column="salesId" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="dispPrice" column="dispPrice" jdbcType="TINYINT"/>
        <result property="createdBy" column="createdBy" jdbcType="BIGINT"/>
        <result property="created" column="created" jdbcType="BIGINT"/>
        <result property="updatedBy" column="updatedBy" jdbcType="BIGINT"/>
        <result property="updated" column="updated" jdbcType="BIGINT"/>
        <result property="isDeleted" column="isDeleted" jdbcType="TINYINT"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,'code',
        outId1,outId2,outType,'name',
        'channel',supermarketAreaId,mnemoCode,
        linkMan,contractNumber,deliveryAddress,
        customerTypeId,customerLineId,adminRegionId,
        customerAreaId,openDate,auditFlag,
        'level',salesId,'status',priceTypeId,
        dispPrice,createdBy,created,linkPosition,
        updatedBy,updated,isDeleted
    </sql>
</mapper>
