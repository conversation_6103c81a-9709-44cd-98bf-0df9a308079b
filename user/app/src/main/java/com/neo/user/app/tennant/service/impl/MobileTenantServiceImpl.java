package com.neo.user.app.tennant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.userinfo.converter.UserMobileConverter;
import com.neo.user.client.tenant.api.MobileTenantService;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.domain.entity.UserMobile;
import com.neo.user.domain.gateway.IUserMobileRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class MobileTenantServiceImpl implements MobileTenantService {

    @Autowired
    private IUserMobileRepository iUserMobileRepository;
    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    @Override
    public SingleResponse<UserMobileInfoDTO> queryMobileInfoByUserId(Long userId, Long tenantId) {
        SingleResponse<UserMobileInfoDTO> response = new SingleResponse<>();
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);
        // 入参校验
        if ((userId <= 0) || null == tenantDomain) {
            log.error("[MobileTenantServiceImpl.queryMobileInfoByUserId][invalid params][userId={},tenantId={}]", userId,
                    tenantId);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("userId", userId);
            queryWrapper.in("domain", tenantDomain.getDomain());
            queryWrapper.eq("deleted", 0);
            UserMobile queryRes = iUserMobileRepository.getOne(queryWrapper);
            // 组装VO对象返回
            response.setSuccess(Boolean.TRUE);
            response.setData(UserMobileConverter.doToVO(queryRes));
            return response;
        } catch (Exception e) {
            log.error("[MobileTenantServiceImpl.queryMobileInfoByUserId][query failed][userId=" + userId + ",tenantId="
                    + tenantId + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    @Override
    public SingleResponse<UserMobileInfoDTO> queryMobileInfoByMobile(String mobile, String areaCode, Long tenantId) {
        SingleResponse<UserMobileInfoDTO> response = new SingleResponse<>();
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);
        // 入参校验
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(areaCode) || null == tenantDomain) {
            log.error(
                    "[MobileTenantServiceImpl.queryMobileInfoByMobile][invalid params][mobile={},areaCode={},tenantId={}]",
                    mobile, areaCode, tenantId);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("mobile", mobile);
            queryWrapper.in("areaCode", areaCode);
            queryWrapper.eq("domain", tenantDomain.getDomain());
            queryWrapper.eq("deleted", 0);
            UserMobile userMobile = iUserMobileRepository.getOne(queryWrapper);
            // 组装VO对象返回
            response.setSuccess(Boolean.TRUE);
            response.setData(UserMobileConverter.doToVO(userMobile));
            return response;
        } catch (Exception e) {
            log.error("[MobileTenantServiceImpl.queryMobileInfoByMobile][query failed][mobile=" + mobile + ",areaCode="
                    + areaCode + ",tenantId=" + tenantId + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    @Override
    public MultiResponse<UserMobileInfoDTO> queryMobileInfoByMobiles(List<String> mobiles, String areaCode, Long tenantId) {
        MultiResponse<UserMobileInfoDTO> response = new MultiResponse<>();
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);
        // 入参校验
        if (CollectionUtils.isEmpty(mobiles) || StringUtils.isEmpty(areaCode) || null == tenantDomain) {
            log.error(
                    "[MobileTenantServiceImpl.queryMobileInfoByMobiles][invalid params][mobiles={},areaCode={},tenantId={}]",
                    mobiles, areaCode, tenantId);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("mobile", mobiles);
            queryWrapper.eq("areaCode", areaCode);
            queryWrapper.eq("domain", tenantDomain.getDomain());
            queryWrapper.eq("deleted", 0);
            List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
            response.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(queryRes)) {
                // 未查询到数据
                return response;
            }
            // 查询到了数据,组装VO对象返回
            List<UserMobileInfoDTO> values = new ArrayList<>();
            for (UserMobile item : queryRes) {
                values.add(UserMobileConverter.doToVO(item));
            }
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[MobileTenantServiceImpl.queryMobileInfoByUserIds][query failed][mobiles=" + mobiles + ",tenantId="
                    + areaCode + ",domain=" + tenantId + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    @Override
    public MultiResponse<UserMobileInfoDTO> queryMobileInfoByUserIds(List<Long> userIds, Long tenantId) {
        MultiResponse<UserMobileInfoDTO> response = new MultiResponse<UserMobileInfoDTO>();
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);
        // 入参校验,不允许domain=all
        if (CollectionUtils.isEmpty(userIds) || null == tenantDomain) {
            log.error("[MobileTenantServiceImpl.queryMobileInfoByUserId][invalid params][userIds={},tenantId={}]", userIds,
                    tenantId);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("userId", userIds);
            queryWrapper.eq("domain", tenantDomain.getDomain());
            queryWrapper.eq("deleted", 0);
            List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
            response.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(queryRes)) {
                // 未查询到数据
                return response;
            }
            // 查询到了数据,组装VO对象返回
            List<UserMobileInfoDTO> values = new ArrayList<>();
            for (UserMobile item : queryRes) {
                values.add(UserMobileConverter.doToVO(item));
            }
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[MobileTenantServiceImpl.queryMobileInfoByUserIds][query failed][userIds=" + userIds + ",tenantId="
                    + tenantId + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }
}
