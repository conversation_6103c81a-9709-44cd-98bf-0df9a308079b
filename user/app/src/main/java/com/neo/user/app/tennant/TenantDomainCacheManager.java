package com.neo.user.app.tennant;

import com.neo.user.client.tenant.dto.EnumSessionDomainDTO;
import com.neo.user.client.tenant.dto.EnumTenantDomainDTO;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.domain.gateway.cache.EnumTenantDomainCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("tenantDomainCacheManager")
@Slf4j
public class TenantDomainCacheManager implements EnumTenantDomainCacheService {

    @Autowired
    private EnumTenantDomainCacheService enumTenantDomainLocalCacheImpl;

//    @Override
//    public EnumTenantDomainDTO getTenantDomainBySuffix(String suffix) {
//        return enumTenantDomainLocalCacheImpl.getTenantDomainBySuffix(suffix);
//    }

//    @Override
//    public EnumTenantDomainDTO getTenantDomainByDomain(String domain) {
//        return enumTenantDomainLocalCacheImpl.getTenantDomainByDomain(domain);
//    }
//
//    @Override
//    public EnumTenantDomainDTO getTenantDomainById(Long id) {
//        return enumTenantDomainLocalCacheImpl.getTenantDomainById(id);
//    }

    @Override
    public EnumSessionDomainDTO getSessionDomainBySuffix(String suffix) {
        return enumTenantDomainLocalCacheImpl.getSessionDomainBySuffix(suffix);
    }

    @Override
    public EnumTenantSessionDomainDTO getTenentSessionDomainBySuffix(String suffix) {
        return enumTenantDomainLocalCacheImpl.getTenentSessionDomainBySuffix(suffix);
    }

    @Override
    public Boolean refreshAll() {
        return enumTenantDomainLocalCacheImpl.refreshAll();
    }

    @Override
    public EnumTenantSessionDomainDTO getSessionDomainByDomain(String domain) {
        return enumTenantDomainLocalCacheImpl.getSessionDomainByDomain(domain);
    }

    public EnumTenantSessionDomainDTO getTenantDomainById(Long tenantId) {
        return enumTenantDomainLocalCacheImpl.getTenantDomainById(tenantId);
    }

    @Override
    public EnumTenantDomainDTO getTenantDomainByTenantIdAndAppId(Long tenantId, String appId) {
        return enumTenantDomainLocalCacheImpl.getTenantDomainByTenantIdAndAppId(tenantId, appId);
    }

    @Override
    public EnumTenantDomainDTO getTenantDomainByDomainAndAppId(String domain, String appId) {
        return enumTenantDomainLocalCacheImpl.getTenantDomainByDomainAndAppId(domain, appId);
    }

}
