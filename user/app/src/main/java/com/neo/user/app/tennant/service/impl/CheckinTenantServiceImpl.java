package com.neo.user.app.tennant.service.impl;

import com.neo.api.SingleResponse;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.client.tenant.api.CheckinTenantService;
import com.neo.user.client.tenant.dto.UserCheckinDTO;
import com.neo.user.client.userinfo.dto.ThirdPartyInfoDTO;
import com.neo.user.domain.gateway.dto.TenantCheckInDTO;
import com.neo.user.domain.gateway.thirdparty.TenantSaasService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.neo.user.domain.constants.UserConstants.DEFAULT_APP_ID;

@Service
@Slf4j
public class CheckinTenantServiceImpl implements CheckinTenantService {

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private TenantSaasService tenantSaasService;

    @Override
    public SingleResponse<Map<Long, List<UserCheckinDTO>>> queryUserCheckinData(Long tenantId, List<Long> userIds, Long startTime, Long endTime) {
        SingleResponse<Map<Long, ThirdPartyInfoDTO>> response = userServiceImpl.queryThirdPartyInfoMapByUserIds(userIds, ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
        if (!response.isSuccess()) {
            return SingleResponse.buildFailure(response.getErrCode(), response.getErrMessage());
        }
        Map<Long, ThirdPartyInfoDTO> userMap = response.getData();
        Map<String, Long> thirdUserIdMap = new HashMap<>();
        List<String> thirdUserIds = new ArrayList<>();
        for (Long userId : userIds) {
            if (userMap.containsKey(userId)) {
                String thirdId2 = userMap.get(userId).getThirdId2();
                thirdUserIds.add(thirdId2);
                thirdUserIdMap.put(thirdId2, userId);
            }
        }
        if (thirdUserIds.isEmpty()) {
            return SingleResponse.buildSuccess(new HashMap<>());
        }
        List<TenantCheckInDTO> checkinDatas = tenantSaasService.getCheckinData(DEFAULT_APP_ID, tenantId, thirdUserIds, startTime, endTime);
        if (CollectionUtils.isEmpty(checkinDatas)) {
            return SingleResponse.buildSuccess(new HashMap<>());
        }
        Map<Long, List<UserCheckinDTO>> resultMap = new HashMap<>();
        checkinDatas.forEach(checkin -> {
            Long userId = thirdUserIdMap.get(checkin.getThirdUserId());
            resultMap.computeIfAbsent(userId, k -> new ArrayList<>()).add(convertToUserCheckinDTO(userId, checkin));
        });
        return SingleResponse.buildSuccess(resultMap);
    }

    private UserCheckinDTO convertToUserCheckinDTO(Long userId, TenantCheckInDTO tenantCheckInDTO) {
        UserCheckinDTO userCheckinDTO = new UserCheckinDTO();
        userCheckinDTO.setUserId(userId);
        userCheckinDTO.setCheckinType(tenantCheckInDTO.getCheckinType());
        userCheckinDTO.setCheckinTime(tenantCheckInDTO.getCheckinTime());
        userCheckinDTO.setExceptionType(tenantCheckInDTO.getExceptionType());
        userCheckinDTO.setLat(Long.valueOf(tenantCheckInDTO.getLat()));
        userCheckinDTO.setLng(Long.valueOf(tenantCheckInDTO.getLng()));
        userCheckinDTO.setScheduleCheckinTime(tenantCheckInDTO.getScheduleCheckinTime());
        userCheckinDTO.setTimelineId(tenantCheckInDTO.getTimelineId());
        return userCheckinDTO;
    }


}
