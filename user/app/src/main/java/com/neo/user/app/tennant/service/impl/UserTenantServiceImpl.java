package com.neo.user.app.tennant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.userinfo.converter.TenantUserConverter;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.client.tenant.api.UserTenantService;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.entity.UserDepartment;
import com.neo.user.domain.gateway.IUserDepartmentRepository;
import com.neo.user.domain.gateway.department.IDepartmentRepository;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserTenantServiceImpl implements UserTenantService {

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private IUserDepartmentRepository iUserDepartmentRepository;

    @Autowired
    private IDepartmentRepository iDepartmentRepository;

    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    @Override
    public SingleResponse<Long> getTenantIdByUserId(Long userId) {
        SingleResponse<UserInfoDTO> userRes = userServiceImpl.queryUserByUserId(userId);
        if (!userRes.isSuccess()){
            return SingleResponse.buildFailure(userRes.getErrCode(),userRes.getErrMessage());
        }
        UserInfoDTO userInfoDTO = userRes.getData();
        EnumTenantSessionDomainDTO sessionDomainByDomain = tenantDomainCacheManager.getSessionDomainByDomain(userInfoDTO.getDomain());
        if (sessionDomainByDomain == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(),"租户不存在");
        }
        return SingleResponse.buildSuccess(sessionDomainByDomain.getTenantId());
    }

    @Override
    public SingleResponse<TenantUserInfoDTO> queryByUserId(Long tenantId, Long userId) {
        EnumTenantSessionDomainDTO tenantDomainDTO = tenantDomainCacheManager.getTenantDomainById(tenantId);
        if (tenantDomainDTO == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        SingleResponse<UserInfoDTO> userRes = userServiceImpl.queryUserByUserId(userId);
        if (!userRes.isSuccess()){
            return SingleResponse.buildFailure(userRes.getErrCode(),userRes.getErrMessage());
        }
        UserInfoDTO userInfoDTO = userRes.getData();
        if (!userInfoDTO.getDomain().equals(tenantDomainDTO.getDomain())){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "不存在");
        }
        TenantUserInfoDTO tenantUserInfoDTO = TenantUserConverter.convertTo(userInfoDTO);
        tenantUserInfoDTO.setTenantId(tenantId);
        LambdaQueryWrapper<UserDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDepartment::getTenantId, tenantId);
        queryWrapper.eq(UserDepartment::getUserId, userId);
        queryWrapper.eq(UserDepartment::getIsDeleted, 0);
        UserDepartment userDepartment = iUserDepartmentRepository.getOne(queryWrapper);
        if (userDepartment != null){
            tenantUserInfoDTO.setDeptId(userDepartment.getDeptId());
            SingleResponse<DepartmentDTO> deptRes = iDepartmentRepository.get(tenantId, userDepartment.getDeptId(), false);
            if (deptRes.isSuccess()){
                tenantUserInfoDTO.setDeptName(deptRes.getData().getName());
            }
        }
        return SingleResponse.buildSuccess(tenantUserInfoDTO);
    }

    @Override
    public SingleResponse<Map<Long, TenantUserInfoDTO>> queryMapByUserIds(Long tenantId, List<Long> userIds) {
        EnumTenantSessionDomainDTO tenantDomainDTO = tenantDomainCacheManager.getTenantDomainById(tenantId);
        if (tenantDomainDTO == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        MultiResponse<UserInfoDTO> userRes = userServiceImpl.queryByUserIds(userIds);
        if (!userRes.isSuccess() || CollectionUtils.isEmpty(userRes.getData())){
            return SingleResponse.buildSuccess(new HashMap<>());
        }
        List<UserInfoDTO> userInfoDTOList = userRes.getData().stream().filter(a -> a.getDomain().equals(tenantDomainDTO.getDomain())).toList();
        if (CollectionUtils.isEmpty(userInfoDTOList)){
            return SingleResponse.buildSuccess(new HashMap<>());
        }
        List<Long> tenantUserIds = userInfoDTOList.stream().map(UserInfoDTO::getUserId).toList();
        LambdaQueryWrapper<UserDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDepartment::getTenantId, tenantId);
        queryWrapper.in(UserDepartment::getUserId, tenantUserIds);
        queryWrapper.eq(UserDepartment::getIsDeleted, 0);
        List<UserDepartment> userDepartments = iUserDepartmentRepository.list(queryWrapper);
        Map<Long, String> deptMap = new HashMap<>();
        Map<Long, Long> userDeptMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userDepartments)){
            // 部门map
            List<Long> deptIds = userDepartments.stream().filter(a -> a.getIsDeleted().equals(0)).map(UserDepartment::getDeptId).distinct().collect(Collectors.toList());
            SingleResponse<Map<Long, String>> deptMapRes = iDepartmentRepository.queryDepartmentNameMap(tenantId, deptIds);
            deptMap = deptMapRes.getData();
            userDeptMap = userDepartments.stream().collect(Collectors.toMap(UserDepartment::getUserId, UserDepartment::getDeptId));

        }
        Map<Long, TenantUserInfoDTO> map = new HashMap<>();
        for (UserInfoDTO userInfoDTO : userInfoDTOList) {
            TenantUserInfoDTO tenantUserInfoDTO = TenantUserConverter.convertTo(userInfoDTO);
            Long deppId = userDeptMap.get(tenantUserInfoDTO.getUserId());
            if (!tenantUserInfoDTO.getIsDeleted() && deppId != null) {
                tenantUserInfoDTO.setDeptName(deptMap.get(deppId));
                tenantUserInfoDTO.setDeptId(deppId);
            }
            map.put(tenantUserInfoDTO.getUserId(), tenantUserInfoDTO);
        }
        return SingleResponse.buildSuccess(map);
    }

    @Override
    public MultiResponse<TenantUserInfoDTO> fuzzyQueryTenantUserByName(Long tenantId, String name, Boolean includeDeleted) {
        // TODO
        return MultiResponse.of(new ArrayList<>());
    }
}
