package com.neo.user.app.userinfo.converter;

import com.alibaba.fastjson.JSON;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.entity.User;
import com.neo.user.client.enums.UserStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UsersConverter {

    private static final Logger LOGGER = LoggerFactory.getLogger(UsersConverter.class);

    public static UserInfoDTO doToVO(final User user) {

        if (null == user) {
            return null;
        }
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(user.getUserId());
        userInfoDTO.setUname(user.getUname());
        userInfoDTO.setUnick(user.getUnick());
        userInfoDTO.setAvatar(user.getAvatar());
        userInfoDTO.setGender(user.getGender());
        if (StringUtils.isNotBlank(user.getRegisterInfo())) {
            try {
                userInfoDTO.setRegisterInfo(JSON.parseObject(user.getRegisterInfo(), Map.class));
            } catch (Exception e) {
                LOGGER.error("[UsersConverter.doToVO][JSON.parseObject exception][registerInfo=" + user.getRegisterInfo() + "]", e);
                Map<String, Object> originData = new HashMap<String, Object>();
                originData.put("originData", user.getRegisterInfo());
                userInfoDTO.setRegisterInfo(originData);
            }
        }

        if (StringUtils.isNotEmpty(user.getExtra())) {
            try {
                userInfoDTO.setExtraInfo(JSON.parseObject(user.getExtra(), Map.class));
            } catch (Exception e) {
                LOGGER.error("[UsersConverter.doToVO][JSON.parseObject exception][extra=" + user.getExtra() + "]", e);
                Map<String, Object> originData = new HashMap<>();
                originData.put("originData", user.getExtra());
                userInfoDTO.setExtraInfo(originData);
            }
        }
        userInfoDTO.setDomain(user.getDomain());
        // 设置用户状态
        List<String> statusList = new ArrayList<>();
        for (UserStatusEnum item : UserStatusEnum.values()) {
            if (user.getStatus() == null) {
                break;
            }
            if (UserStatusEnum.checkUserStatus(user.getStatus(), item.getIndex())) {
                statusList.add(item.getName());
            }
        }
        userInfoDTO.setStatusList(statusList);
        userInfoDTO.setCreated(user.getCreated());
        userInfoDTO.setUpdated(user.getUpdated());
        return userInfoDTO;
    }
}
