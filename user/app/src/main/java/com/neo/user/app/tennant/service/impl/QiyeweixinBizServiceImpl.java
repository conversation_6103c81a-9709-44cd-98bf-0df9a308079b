package com.neo.user.app.tennant.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.alibaba.fastjson.JSON;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.app.qiyeweixin.aes.AesException;
import com.neo.user.app.qiyeweixin.aes.WXBizMsgXmlCrypt;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.tennant.dto.TenantManageDTO;
import com.neo.user.app.tennant.dto.TenantUserRegisterDTO;
import com.neo.user.app.tennant.service.QiyeweixinBizService;
import com.neo.user.app.tennant.service.TenantBizService;
import com.neo.user.app.userinfo.handler.RegisterBizHandler;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.userinfo.dto.ThirdPartyRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.gateway.department.IDepartmentRepository;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import com.neo.user.domain.gateway.dto.TenantThirdPartyDTO;
import com.neo.user.domain.gateway.thirdparty.TenantSaasService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Slf4j
public class QiyeweixinBizServiceImpl implements QiyeweixinBizService {

    @Value("${wx.qy.token}")
    private String token;

    @Value("${wx.qy.encodingAESKey}")
    private String encodingAESKey;

    @Value("${wx.qy.corpId}")
    private String corpId;

//    @Value("${wx.qy.suiteId}")
//    private String suiteId;

    @Value("${wx.qy.web.suiteId}")
    private String webSuiteId;

    @Value("${user.tenant.default.sessionDomain}")
    private String defaultSessionDomain;

    @Autowired
    private Environment env;

    public String getSuiteId(String appId) {
        String key = "wx.qy." + appId.toLowerCase() + ".suiteId";
        return env.getProperty(key);
    }

    @Autowired
    private TenantSaasService tenantSaasService;

    @Autowired
    private TenantBizService tenantBizService;

    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    @Autowired
    private RegisterBizHandler registerBizHandler;

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private IDepartmentRepository departmentRepository;

    @Override
    public String getCallbackVerify(String sVerifyMsgSig, String sVerifyTimeStamp, String sVerifyNonce, String sVerifyEchoStr) {
        String sToken = token;
        String sCorpID =  corpId;// TODO
        String sEncodingAESKey = encodingAESKey;

        WXBizMsgXmlCrypt wxcpt = null;
        try {
            wxcpt = new WXBizMsgXmlCrypt(sToken, sEncodingAESKey, sCorpID);
        }catch (AesException E){
            return "error";
        }
		/*
		------------使用示例一：验证回调URL---------------
		*企业开启回调模式时，企业微信会向验证url发送一个get请求
		假设点击验证时，企业收到类似请求：
		* GET /cgi-bin/wxpush?msg_signature=5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3&timestamp=1409659589&nonce=263014780&echostr=P9nAzCzyDtyTWESHep1vC5X9xho%2FqYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp%2B4RPcs8TgAE7OaBO%2BFZXvnaqQ%3D%3D
		* HTTP/1.1 Host: qy.weixin.qq.com

		接收到该请求时，企业应
		1.解析出Get请求的参数，包括消息体签名(msg_signature)，时间戳(timestamp)，随机数字串(nonce)以及企业微信推送过来的随机加密字符串(echostr),这一步注意作URL解码。
		2.验证消息体签名的正确性
		3.解密出echostr原文，将原文当作Get请求的response，返回给企业微信
		第2，3步可以用企业微信提供的库函数VerifyURL来实现。

		*/
        String sEchoStr; //需要返回的明文
        try {
            sEchoStr = wxcpt.VerifyURL(sVerifyMsgSig, sVerifyTimeStamp,
                    sVerifyNonce, sVerifyEchoStr);
//            log.info("verifyurl echostr: " + sEchoStr);
            // 验证URL成功，将sEchoStr返回
            // HttpUtils.SetResponse(sEchoStr);
        } catch (Exception e) {
            //验证URL失败，错误原因请查看异常
            log.error("企业微信回调验证失败",e);
            return "error";
        }
        return  sEchoStr;
    }

    @Override
    public Boolean handleDataMsg(String appId, String xml, String msgSignature, String timestamp, String nonce) {
        JSONObject xml2json = XML.toJSONObject(xml);

        String sToken = token;
        String sCorpID =  xml2json.getJSONObject("xml").getStr("ToUserName", corpId);// TODO
        String sEncodingAESKey = encodingAESKey;

        WXBizMsgXmlCrypt crypt = null;
        try {

            crypt = new WXBizMsgXmlCrypt(sToken, sEncodingAESKey, sCorpID);
            String data = crypt.DecryptMsg(msgSignature, timestamp, nonce, xml);
//            log.info("【企业微信数据回调】XML: {}", data);
            JSONObject jsonObject = XML.toJSONObject(data);
            log.info("【企业微信数据回调】appId: {} JSON: {}", appId, jsonObject.getJSONObject("xml").toString());

//            BaseEventData eventData = EventDataManager.getXmlData(data);
            return true;
        }catch (AesException e){
            log.error("【企业微信数据回调】XML解析错误", e);
            return false;
        }
    }


    private Boolean processChangeContact(String appId, JSONObject jsonObject) {
        String changeType = jsonObject.getStr("ChangeType");
        String authCorpId = jsonObject.getStr("AuthCorpId");
        String openId = jsonObject.getStr("OpenUserID");

        EnumTenantSessionDomainDTO sessionDomain = tenantDomainCacheManager.getSessionDomainByDomain(authCorpId);

        switch (changeType) {
            case "delete_user":
                SingleResponse<UserInfoDTO> userInfoDTOSingleResponse = userServiceImpl.queryByUserThirdParty(openId, ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode(), sessionDomain.getDomain());
                if (userInfoDTOSingleResponse.isSuccess() && userInfoDTOSingleResponse.getData() != null) {
                    UserInfoDTO userInfoDTO = userInfoDTOSingleResponse.getData();
                    log.info("【企业微信数据回调】删除用户 appId: {} tenantId={} userId={}", appId, sessionDomain.getTenantId(), userInfoDTO.getUserId());
                    tenantBizService.deleteUser(sessionDomain.getTenantId(), userInfoDTO.getUserId());
                }
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public Boolean handleCommand(String appId, String xml, String msgSignature, String timestamp, String nonce) {
        String sToken = token;
        String sCorpID =  getSuiteId(appId);// TODO
        String sEncodingAESKey = encodingAESKey;

        WXBizMsgXmlCrypt crypt = null;
        try {
            crypt = new WXBizMsgXmlCrypt(sToken, sEncodingAESKey, sCorpID);
            String data = crypt.DecryptMsg(msgSignature, timestamp, nonce, xml);
//            log.info("【企业微信指令回调】XML: {}", data);
            JSONObject jsonObject = XML.toJSONObject(data);
            log.info("【企业微信指令回调】appId: {} JSON: {}",appId , jsonObject.getJSONObject("xml").toString());
            return processCommand(appId, jsonObject.getJSONObject("xml"));
        }catch (AesException e){
            log.error("【企业微信指令回调】XML解析错误", e);
            return false;
        }
    }

    private Boolean processCommand(String appId, JSONObject jsonObject) {
        String suiteId = jsonObject.getStr("SuiteId");
        String infoType = jsonObject.getStr("InfoType");

        switch (infoType) {
            case "suite_ticket":
                // 更新ticket
                String suiteTicket = jsonObject.getStr("SuiteTicket");
                tenantSaasService.refreshSuiteTicket( appId,  suiteTicket);
                log.info("【企业微信Saas】appId：{} 更新SuiteTicket：{}", appId, suiteTicket);
                break;
            case "create_auth":
                log.info("【企业微信Saas】应用授权回调 appId: {}", appId);
                // 创建租户
                String authCode = jsonObject.getStr("AuthCode");//临时授权码，10分钟内有效
                TenantThirdPartyDTO tenantThirdPartyDTO = tenantSaasService.getTenantThirdParty(appId, authCode);
                if (tenantThirdPartyDTO != null && tenantThirdPartyDTO.getTenantId() == null){
                    ExecutorService executor = Executors.newSingleThreadExecutor();
                    executor.submit(() -> {
                        initTenantAndUser(appId, tenantThirdPartyDTO);
                    });
                    executor.shutdown();
                    return true;
                } else {
                    log.info("【企业微信Saas】已存在的租户 appId :{} tenantId={}", appId, tenantThirdPartyDTO.getTenantId());
                }
                break;
            case "cancel_auth":
                // TODO 删除租户
                String authCorpId = jsonObject.getStr("AuthCorpId");//
                break;
            case "change_contact":
                processChangeContact(appId, jsonObject);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public TenantUserInfoDTO addTenantUser(String appId, TenantThirdPartyDTO tenantThirdPartyDTO) {
        Long tenantId = tenantThirdPartyDTO.getTenantId();
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);
        String uname = tenantDomain.getDomain() + "_" + tenantThirdPartyDTO.getThirdUserId();

        Long rootDeptId = null;
        MultiResponse<DepartmentDTO> allDepartment = departmentRepository.getAllDepartment(tenantId);
        if (CollectionUtils.isEmpty(allDepartment.getData())){
            log.warn("【企业微信Saas】获取根部门失败 tenantId="+ tenantId);
//            return null;
        } else{
            rootDeptId = allDepartment.getData().get(0).getDeptId();
        }

        TenantUserRegisterDTO registerDTO = new TenantUserRegisterDTO();
        registerDTO.setTenantId(tenantId);
        registerDTO.setUname(uname);
        registerDTO.setUnick(tenantThirdPartyDTO.getThirdUserName());
//        registerDTO.setJobNumber(tenantThirdPartyDTO.getThirdUserId());
        registerDTO.setPassword(tenantThirdPartyDTO.getThirdUserOpenId());//使用openid作为初始密码
        registerDTO.setConfirmPassword(tenantThirdPartyDTO.getThirdUserOpenId());
        registerDTO.setIsVirtual(false);
        registerDTO.setDeptId(rootDeptId);// 获取根部门id
        SingleResponse<TenantUserInfoDTO> res1 = tenantBizService.addUser(registerDTO);
        if (!res1.isSuccess()){
            log.error("【企业微信Saas】创建初始用户失败 uname=" + uname + " res：" + JSON.toJSONString(res1));
            return null;
        }
        TenantUserInfoDTO tenantUserInfoDTO = res1.getData();
        // TODO 绑定用户三方信息
        ThirdPartyRegisterUserInfoDTO thirdPartyRegisterUserInfoDTO = new ThirdPartyRegisterUserInfoDTO();
        thirdPartyRegisterUserInfoDTO.setUserId(tenantUserInfoDTO.getUserId());
        thirdPartyRegisterUserInfoDTO.setAppId(appId);
        thirdPartyRegisterUserInfoDTO.setThirdId(tenantThirdPartyDTO.getThirdUserOpenId());// 三方openId作为thridId
        thirdPartyRegisterUserInfoDTO.setThirdId2(tenantThirdPartyDTO.getThirdUserId());// 三方userId作为thirdId2
        thirdPartyRegisterUserInfoDTO.setThirdType(ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
        thirdPartyRegisterUserInfoDTO.setDomain(tenantDomain.getDomain());
        Boolean res2 = registerBizHandler.bindExistUser(thirdPartyRegisterUserInfoDTO, tenantUserInfoDTO.getUserId());
        return tenantUserInfoDTO;
    }

    @Override
    public Boolean initTenantAndUser(String appId, TenantThirdPartyDTO tenantThirdPartyDTO){
        // TODO 创建租户
        Long tenantId = createTenant(tenantThirdPartyDTO);
        if (tenantId == null){
            log.error("【企业微信Saas】创建租户失败 " + JSON.toJSONString(tenantThirdPartyDTO));
            return false;
        }
        log.info("【企业微信Saas】创建租户成功：tenantId=" + tenantId);
        // TODO 创建初始用户
        TenantUserInfoDTO tenantUserInfoDTO = addTenantUser(appId, tenantThirdPartyDTO);
        if (tenantUserInfoDTO == null ){
            log.error("【企业微信Saas】获取初始用户信息失败 + uname=" + tenantThirdPartyDTO.getCorpId() + "_" + tenantThirdPartyDTO.getThirdUserId());
            return false;
        }
        log.info("【企业微信Saas】创建初始用户成功：tenantId={} userId={}", tenantId,  tenantUserInfoDTO.getUserId());
        // TODO 绑定APP
        SingleResponse<Boolean> res3 = tenantBizService.bindApp(tenantId, tenantUserInfoDTO.getUserId(), appId);
        if (!res3.isSuccess()){
            log.error("【企业微信Saas】绑定APP失败 tenantId=" + tenantId + " userId=" + tenantUserInfoDTO.getUserId() + " appId=" + appId + " res:" + JSON.toJSONString(res3));
            return false;
        }
        log.info("【企业微信Saas】租户授权完成");
        return true;
    }

    @Override
    public Boolean handleWeb(String xml, String msgSignature, String timestamp, String nonce) {
        String sToken = token;
        String sCorpID =  webSuiteId;
        String sEncodingAESKey = encodingAESKey;

        WXBizMsgXmlCrypt crypt = null;
        try {
            crypt = new WXBizMsgXmlCrypt(sToken, sEncodingAESKey, sCorpID);
            String data = crypt.DecryptMsg(msgSignature, timestamp, nonce, xml);
            log.info("【企业微信Web指令回调】XML: {}", data);
            JSONObject jsonObject = XML.toJSONObject(data);
            log.info("【企业微信Web指令回调】JSON: {}", jsonObject.getJSONObject("xml").toString());
            return processWeb(jsonObject.getJSONObject("xml"));
        }catch (AesException e){
            log.error("【企业微信Web指令回调】XML解析错误", e);
            return false;
        }
    }

    private Boolean processWeb(JSONObject jsonObject) {
        String suiteId = jsonObject.getStr("SuiteId");
        String infoType = jsonObject.getStr("InfoType");

        switch (infoType) {
            case "suite_ticket":
                // 更新ticket
                String suiteTicket = jsonObject.getStr("SuiteTicket");
                tenantSaasService.refreshWebSuiteTicket(suiteTicket);
                log.info("【企业微信Saas Web】更新WebSuiteTicket：{}", suiteTicket);
                break;
            default:
                log.info("【企业微信Saas Web】" + jsonObject.toString());
                break;
        }
        return true;
    }

    private Long createTenant(TenantThirdPartyDTO tenantThirdPartyDTO) {
        Long tenantId = null;
        EnumTenantSessionDomainDTO sessionDomainByDomain = tenantDomainCacheManager.getSessionDomainByDomain(tenantThirdPartyDTO.getCorpId());
        if (sessionDomainByDomain == null){// 租户未创建
            TenantManageDTO tenantManageDTO = new TenantManageDTO();
            tenantManageDTO.setName(tenantThirdPartyDTO.getName());
            tenantManageDTO.setCompany(tenantThirdPartyDTO.getCorpName());
            tenantManageDTO.setSuffix(tenantThirdPartyDTO.getCorpId());
            tenantManageDTO.setDomain(tenantThirdPartyDTO.getCorpId());
            tenantManageDTO.setSessionDomain(defaultSessionDomain);
            tenantManageDTO.setLogo(tenantThirdPartyDTO.getLogo());
            tenantManageDTO.setMaxShare(3);
            tenantManageDTO.setCheckDid(false);

            SingleResponse<Long> tenantRes = tenantBizService.createTenant(tenantManageDTO);
            if (!tenantRes.isSuccess()) {
                return null;
            }
            tenantId = tenantRes.getData();
        } else {
            // 已存在corpId相同的租户
            tenantId = sessionDomainByDomain.getTenantId();
            log.info("【企业微信Saas】租户已存在，不重复创建，tenantId=" + tenantId);
        }
        if (tenantId != null){
            try {
                tenantThirdPartyDTO.setTenantId(tenantId);
                tenantThirdPartyDTO.setAppId(tenantThirdPartyDTO.getAppId());
                tenantSaasService.saveTenantThirdParty(tenantThirdPartyDTO);
            } catch (Exception e){
                log.warn(e.getMessage(), e);
            }
        }
        return tenantId;
    }

}
