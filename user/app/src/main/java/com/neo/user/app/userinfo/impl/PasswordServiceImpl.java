package com.neo.user.app.userinfo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.client.userinfo.api.PasswordService;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.domain.entity.UserPwd;
import com.neo.user.domain.gateway.IUserPwdRepository;
import com.neo.user.domain.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.beans.factory.annotation.Autowired;

@Service
@Slf4j
public class PasswordServiceImpl implements PasswordService {
    @Autowired
    private IUserPwdRepository iUserPwdRepository;
    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    /**
     * 是否设置过密码
     *
     * @param userId 用户ID
     * @param domain 用户业务域
     * @return true:已经设置过密码,false:未设置密码
     */
    @Override
    public SingleResponse<Boolean> isPasswordSet(Long userId, String domain) {
        SingleResponse<Boolean> response = new SingleResponse<>();

        // 入参校验
        if ((userId <= 0) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[PasswordServiceImpl.isPasswordSet][invalid params][userId={},domain={}]", userId, domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            QueryWrapper<UserPwd> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("userId", userId);
            queryWrapper.eq("domain", domain);
            UserPwd queryRes = iUserPwdRepository.getOne(queryWrapper);
            // 组装VO对象返回
            response.setSuccess(Boolean.TRUE);
            response.setData((null == queryRes) ? Boolean.FALSE : Boolean.TRUE);
            return response;
        } catch (Exception e) {
            log.error("[PasswordServiceImpl.isPasswordSet][query failed][userId=" + userId + ",domain=" + domain
                    + "]", e);
            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 验证密码是否正确
     *
     * @param userId   用户ID
     * @param password md5(原文密码),即原文密码的md5值
     * @param domain   用户业务域
     * @return true:密码校验正确,false:密码校验错误
     */
    @Override
    public SingleResponse<Boolean> checkPassword(Long userId, String password, String domain) {
        SingleResponse<Boolean> response = new SingleResponse<>();

        // 入参校验
        if ((userId <= 0) || StringUtils.isEmpty(password) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[PasswordServiceImpl.checkPassword][invalid params][userId={},domain={}]", userId, domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            response.setData(Boolean.FALSE);
            return response;
        }
        // 从DB查询密码数据进行校验核对
        try {
            QueryWrapper<UserPwd> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("userId", userId);
            queryWrapper.eq("domain", domain);
            UserPwd queryRes = iUserPwdRepository.getOne(queryWrapper);
            if (null == queryRes) {
                // 如果密码不存在,直接返回错误
                response.setErrCode(UserConstants.ERROR_USER_PASSWORD_NO_EXIST);
                response.setErrMessage("未设置密码");
                response.setData(Boolean.FALSE);
                return response;

            }
            // 密码数据校验核对
            String encryptPassword = CommonUtil.generatePassword(queryRes.getSalt(), password, queryRes.getExtra(),
                    domain);
            if (StringUtils.equals(encryptPassword, queryRes.getPwd())) {
                response.setSuccess(Boolean.TRUE);
                response.setData(Boolean.TRUE);
            } else {
                response.setErrCode(UserConstants.ERROR_LOGIN_WRONG_PWD);
                response.setErrMessage("Incorrect password");
                response.setData(Boolean.FALSE);
            }
            return response;
        } catch (Exception e) {
            log.warn("[PasswordServiceImpl.checkPassword][check password failure][userId=" + userId + ",password="
                    + password + ",domain=" + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            response.setData(Boolean.FALSE);
            return response;
        }
    }

    /**
     * 设置或者更新密码
     *
     * @param userId   用户ID
     * @param password md5(原文密码),即原文密码的md5值
     * @param domain   用户业务域
     * @return true:密码更新成功,false:密码更新失败
     */
    @Override
    public SingleResponse<Boolean> setPassword(Long userId, String password, String domain) {
        SingleResponse<Boolean> response = new SingleResponse<>();

        // 入参校验
        if ((userId <= 0) || StringUtils.isEmpty(password) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[PasswordServiceImpl.setPassword][invalid params][userId={},password={},domain={}]", userId,
                    password, domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            response.setData(Boolean.FALSE);
            return response;
        }
        try {
            boolean result = updatePassword(userId, password, domain);
            if (!result) {
                log.error(
                        "[PasswordServiceImpl.setPassword][set password failure][userId={},password={},domain={}]",
                        userId, password, domain);
                response.setErrCode(UserConstants.ERROR_USER_PWD_SET);
                response.setErrMessage("set password failed");
                response.setData(Boolean.FALSE);
                return response;
            }
            response.setData(Boolean.TRUE);
            response.setSuccess(Boolean.TRUE);
            return response;
        } catch (Exception e) {
            log.warn("[PasswordServiceImpl.setPassword][set password failure][userId=" + userId + ",password="
                    + password + ",domain=" + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            response.setData(Boolean.FALSE);
            return response;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(long userId, String password, String domain) {
        // 从DB中查询数据,没有则创建密码,有则执行更新密码
        QueryWrapper<UserPwd> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("userId", userId);
        queryWrapper.eq("domain", domain);
        UserPwd userPwd = iUserPwdRepository.getOne(queryWrapper);
        boolean result;
        if (null == userPwd) {
            // 执行密码创建
            // 构建DB数据
            userPwd = new UserPwd();
            userPwd.setUserId(userId);
            userPwd.setPwd(CommonUtil.generatePassword(userPwd.getSalt(), password, String.valueOf(userId), domain));
            userPwd.setDomain(domain);
            userPwd.setSalt(CommonUtil.getRandomString(10));
            userPwd.setExtra(String.valueOf(userId));
            userPwd.setCreated(CommonUtil.getCurrentSeconds());
            userPwd.setUpdated(CommonUtil.getCurrentSeconds());
            // 落地DB数据
            result = iUserPwdRepository.save(userPwd);
        } else {
            // 执行密码更新
            UpdateWrapper<UserPwd> updateWrapper = new UpdateWrapper<>();
            userPwd.setSalt(CommonUtil.getRandomString(10));
            updateWrapper.set("salt", userPwd.getSalt());
            updateWrapper.set("pwd", CommonUtil.generatePassword(userPwd.getSalt(), password, String.valueOf(userId), domain));
            updateWrapper.set("updated", CommonUtil.getCurrentSeconds());
            updateWrapper.eq("domain", domain);
            updateWrapper.eq("userId", userId);
            // 更新DB数据
            result = iUserPwdRepository.update(updateWrapper);
        }
        return result;
    }
}
