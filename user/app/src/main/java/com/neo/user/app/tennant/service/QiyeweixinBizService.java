package com.neo.user.app.tennant.service;

import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.domain.gateway.dto.TenantThirdPartyDTO;

public interface QiyeweixinBizService {

    public String getCallbackVerify(String sVerifyMsgSig, String sVerifyTimeStamp, String sVerifyNonce, String sVerifyEchoStr);

    public Boolean handleDataMsg(String appId, String xml, String msgSignature, String timestamp, String nonce);

    public Boolean handleCommand(String appId, String xml, String msgSignature, String timestamp, String nonce);

    TenantUserInfoDTO addTenantUser(String appId, TenantThirdPartyDTO tenantThirdPartyDTO);

    public Boolean initTenantAndUser(String appId, TenantThirdPartyDTO tenantThirdPartyDTO);

    public Boolean handleWeb(String xml, String msgSignature, String timestamp, String nonce);
}
