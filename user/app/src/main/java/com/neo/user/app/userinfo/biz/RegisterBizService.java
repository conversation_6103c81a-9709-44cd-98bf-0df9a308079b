package com.neo.user.app.userinfo.biz;


import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.common.utils.IdConvertor;
import com.neo.session.Constants;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.userinfo.biz.dto.CookieItem;
import com.neo.user.app.userinfo.biz.dto.LoginItem;
import com.neo.user.app.userinfo.impl.MobileServiceImpl;
import com.neo.user.app.userinfo.impl.RegisterServiceImpl;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.app.verify.impl.SessionServiceImpl;
import com.neo.user.client.userinfo.dto.CommonRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.MobileRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import com.neo.user.domain.constants.UserConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class RegisterBizService {

    @Autowired
    private MobileServiceImpl mobileServiceImpl;

    @Autowired
    private RegisterServiceImpl registerServiceImpl;

    @Autowired
    private SessionServiceImpl sessionServiceImpl;

    @Autowired
    private UserServiceImpl userServiceImpl;
    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    public SingleResponse<LoginItem> register(CommonRegisterUserInfoDTO commonRegisterUserInfoDTO) {
        SingleResponse<LoginItem> response = new SingleResponse<>();
        SingleResponse<UserInfoDTO> exists = userServiceImpl.queryByUserName(commonRegisterUserInfoDTO.getUname(), commonRegisterUserInfoDTO.getDomain());
        if (exists.isSuccess() && exists.getData() != null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "用户名已存在");
        }
        SingleResponse<UserInfoDTO> registerResponse = registerServiceImpl.register(commonRegisterUserInfoDTO);
        if (registerResponse.isSuccess() && registerResponse.getData() != null) {
            LoginItem loginItem = new LoginItem();
            UserInfoDTO userInfoDTO = registerResponse.getData();
            SingleResponse<String> signRes = sessionServiceImpl.getSign(userInfoDTO.getUserId(),
                    commonRegisterUserInfoDTO.getDeviceId(), commonRegisterUserInfoDTO.getDomain());
            loginItem.setSign(signRes.getData());
            loginItem.setUname(userInfoDTO.getUname());
            loginItem.setUid(IdConvertor.idToUrl(userInfoDTO.getUserId()));
            List<CookieItem> cookieItems = new ArrayList<>();
            cookieItems.add(new CookieItem(tenantDomainCacheManager.getSessionDomainByDomain(
                    commonRegisterUserInfoDTO.getDomain()).getSessionDomain(), Constants.COOKIE_SIGN_NAME, signRes.getData()));
            loginItem.setCookies(cookieItems);
            response.setSuccess(Boolean.TRUE);
            response.setData(loginItem);
            return response;
        } else {
            response.setErrCode(registerResponse.getErrCode());
            response.setErrMessage(registerResponse.getErrMessage());
            return response;
        }
    }

    public SingleResponse<LoginItem> registerByMobile(MobileRegisterUserInfoDTO mobileRegisterUserInfo) {
        SingleResponse<LoginItem> response = new SingleResponse<>();
        SingleResponse<UserMobileInfoDTO> mobileInfoDTOSingleResponse = mobileServiceImpl.queryMobileInfoByMobile(mobileRegisterUserInfo.getMobile(),
                mobileRegisterUserInfo.getAreaCode(), mobileRegisterUserInfo.getDomain());
        if (mobileInfoDTOSingleResponse.isSuccess() && mobileInfoDTOSingleResponse.getData() != null) {
            response.setErrCode(UserConstants.MOBILE_ALREADY_REGISTER);
            response.setErrMessage("手机号已注册");
            return response;
        }
        SingleResponse<UserInfoDTO> registerResponse = registerServiceImpl.registerByMobile(mobileRegisterUserInfo);
        if (registerResponse.isSuccess() && registerResponse.getData() != null) {
            LoginItem loginItem = new LoginItem();
            UserInfoDTO userInfoDTO = registerResponse.getData();
            SingleResponse<String> signRes = sessionServiceImpl.getSign(userInfoDTO.getUserId(),
                    mobileRegisterUserInfo.getDeviceId(), mobileRegisterUserInfo.getDomain());
            loginItem.setSign(signRes.getData());
            loginItem.setUname(userInfoDTO.getUname());
            loginItem.setUid(IdConvertor.idToUrl(userInfoDTO.getUserId()));
            List<CookieItem> cookieItems = new ArrayList<>();
            cookieItems.add(new CookieItem(tenantDomainCacheManager.getSessionDomainByDomain(
                    mobileRegisterUserInfo.getDomain()).getSessionDomain(), Constants.COOKIE_SIGN_NAME, signRes.getData()));
            loginItem.setCookies(cookieItems);
            response.setSuccess(Boolean.TRUE);
            response.setData(loginItem);
            return response;
        } else {
            response.setErrCode(registerResponse.getErrCode());
            response.setErrMessage(registerResponse.getErrMessage());
            return response;
        }
    }
}
