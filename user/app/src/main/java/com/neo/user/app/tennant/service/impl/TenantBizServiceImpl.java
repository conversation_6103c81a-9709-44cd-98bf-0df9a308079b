package com.neo.user.app.tennant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.tennant.dto.TenantManageDTO;
import com.neo.user.app.tennant.dto.TenantUserUpdateDTO;
import com.neo.user.app.userinfo.converter.TenantUserConverter;
import com.neo.user.app.userinfo.impl.MobileServiceImpl;
import com.neo.user.app.verify.impl.SessionServiceImpl;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.app.tennant.dto.TenantUserRegisterDTO;
import com.neo.user.app.tennant.params.TenantUserQueryDTO;
import com.neo.user.app.userinfo.impl.RegisterServiceImpl;
import com.neo.user.client.enums.UserStatusEnum;
import com.neo.user.client.userinfo.api.MobileService;
import com.neo.user.client.userinfo.dto.CommonRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import com.neo.user.domain.entity.*;
import com.neo.user.domain.gateway.*;
import com.neo.user.domain.gateway.auth.IUserAuthRepository;
import com.neo.user.domain.gateway.department.IDepartmentRepository;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import com.neo.user.app.tennant.dto.TenantDTO;
import com.neo.user.app.tennant.service.TenantBizService;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.neo.user.client.userinfo.Constants.Constants.USER_STATUS_DELETED;
import static com.neo.user.client.userinfo.Constants.Constants.USER_STATUS_NO_LOGIN;

/**
 * 租户service
 */
@Service
@Slf4j
public class TenantBizServiceImpl implements TenantBizService {

    @Autowired
    private ITenantRepository iTenantRepository;

    @Autowired
    private IEnumTenantDomainRepository iEnumTenantDomainRepository;

    @Autowired
    private IEnumSessionDomainRepository iEnumSessionDomainRepository;

    @Autowired
    private IEnumAppDomainAuthRepository iEnumAppDomainAuthRepository;

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private IUserRepository iUserRepository;

    @Autowired
    private RegisterServiceImpl registerServiceImpl;

    @Autowired
    private IUserDepartmentRepository iUserDepartmentRepository;

    @Autowired
    private IDepartmentRepository iDepartmentRepository;

    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    @Autowired
    private SessionServiceImpl sessionServiceImpl;

    @Autowired
    private UserTenantServiceImpl userTenantServiceImpl;

    @Autowired
    private IUserAuthRepository iUserAuthRepository;

    @Autowired
    private IAppAuthTemplateRepository iAppAuthTemplateRepository;

    @Autowired
    private MobileServiceImpl mobileServiceImpl;
    @Autowired
    private IUserMobileRepository iUserMobileRepository;

    @Override
    public SingleResponse<TenantDTO> getById(Long id) {
        Tenant tenant = iTenantRepository.getById(id);
        if (tenant == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setId(tenant.getId());
        tenantDTO.setName(tenant.getName());
        tenantDTO.setLogo(tenant.getLogo());
        tenantDTO.setCompany(tenant.getCompany());
        tenantDTO.setBackgroundImage(tenant.getBackgroundImage());
        return SingleResponse.buildSuccess(tenantDTO);
    }

    @Override
    public SingleResponse<Long> createTenant(TenantManageDTO tenantManageDTO) {
        Long tenantId = initTenant(tenantManageDTO);
        if (tenantId == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.FAIL.getCode() , "创建租户失败");
        } else {
            log.info("【租户】创建租户成功 tenantId={}", tenantId);
        }
        // 初始化部门
        SingleResponse<Boolean> initDeptRes = iDepartmentRepository.init(tenantId, tenantManageDTO.getDomain());
        if (!initDeptRes.isSuccess()){
            return SingleResponse.buildFailure(initDeptRes.getErrCode(), "创建部门失败");
        }
        DepartmentDTO departmentDTO = new DepartmentDTO();
        departmentDTO.setTenantId(tenantId);
        departmentDTO.setParentId(0L);
        departmentDTO.setName(tenantManageDTO.getName());
        SingleResponse<Boolean> addDeptRes = iDepartmentRepository.add(departmentDTO);
        if (!addDeptRes.isSuccess()){
            return SingleResponse.buildFailure(addDeptRes.getErrCode(), "创建默认部门失败");
        } else {
            log.info("【租户】初始化部门成功 根部门:" + departmentDTO.getName());
        }
        return SingleResponse.buildSuccess(tenantId);
    }

    @Transactional
    protected Long initTenant(TenantManageDTO tenantManageDTO) {
        Tenant tenant = new Tenant();
        tenant.setName(tenantManageDTO.getName());
        tenant.setLogo(tenantManageDTO.getLogo());
        tenant.setCompany(tenantManageDTO.getCompany());
        tenant.setBackgroundImage(tenantManageDTO.getBackgroundImage());
        tenant.setCreated(CommonUtil.getCurrentSeconds());
        tenant.setUpdated(CommonUtil.getCurrentSeconds());
        boolean res = iTenantRepository.save(tenant);

        Long tenantId = tenant.getId();
        EnumTenantDomain enumTenantDomain = new EnumTenantDomain();
        enumTenantDomain.setTenantId(tenantId);
        enumTenantDomain.setDomain(tenantManageDTO.getDomain());
        enumTenantDomain.setCreated(CommonUtil.getCurrentSeconds());
        enumTenantDomain.setUpdated(CommonUtil.getCurrentSeconds());
        boolean res1 = iEnumTenantDomainRepository.save(enumTenantDomain);

        EnumSessionDomain enumSessionDomain = new EnumSessionDomain();
        enumSessionDomain.setDomain(tenantManageDTO.getDomain());
        enumSessionDomain.setSessionDomain(tenantManageDTO.getSessionDomain());
        enumSessionDomain.setSuffix(tenantManageDTO.getSuffix());
        enumSessionDomain.setMaxShare(tenantManageDTO.getMaxShare());
        enumSessionDomain.setCheckDid(tenantManageDTO.getCheckDid()?1:0);
        enumSessionDomain.setCreated(CommonUtil.getCurrentSeconds());
        enumSessionDomain.setUpdated(CommonUtil.getCurrentSeconds());
        boolean res2 = iEnumSessionDomainRepository.save(enumSessionDomain);
        if (!res || !res1 || !res2){
            return null;
        }
        tenantDomainCacheManager.refreshAll();
        return tenantId;
    }

    @Override
    public SingleResponse<Boolean> update(TenantDTO tenantDTO) {
        if (tenantDTO.getId() == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        Tenant tenant = new Tenant();
        tenant.setId(tenantDTO.getId());
        tenant.setName(tenantDTO.getName());
        tenant.setLogo(tenantDTO.getLogo());
        tenant.setCompany(tenantDTO.getCompany());
        tenant.setBackgroundImage(tenantDTO.getBackgroundImage());
        tenant.setUpdated(CommonUtil.getCurrentSeconds());
        boolean res = iTenantRepository.updateById(tenant);
        if (res){
            return SingleResponse.buildSuccess(true);
        }
        return SingleResponse.buildFailure(GlobalResponseCodeEnum.FAIL);
    }

    @Override
    public PageResponse<TenantUserInfoDTO> queryTenantUser(TenantUserQueryDTO tenantUserQueryDTO) {
        Long tenantId = tenantUserQueryDTO.getTenantId();
        int pageNum = tenantUserQueryDTO.getPageIndex();
        int pageSize = tenantUserQueryDTO.getPageSize();

        Boolean filterUser = false;
        List<Long> filterUserIds = new ArrayList<>();
        LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(tenantUserQueryDTO.getJobNumer())) {//工号
            userLambdaQueryWrapper.like(User::getUname, tenantUserQueryDTO.getJobNumer());
            filterUser = true;
        }
        if (StringUtils.isNotEmpty(tenantUserQueryDTO.getUnick())){//姓名
            userLambdaQueryWrapper.like(User::getUnick, tenantUserQueryDTO.getUnick());
            filterUser = true;
        }
        if (tenantUserQueryDTO.getIsDeleted()){
            filterUser = true;
            userLambdaQueryWrapper.notIn(User::getStatus, UserStatusEnum.getValuesWithoutDeleted());
        } else {
            userLambdaQueryWrapper.in(User::getStatus, UserStatusEnum.getValuesWithoutDeleted());

        }
        if (filterUser){
            List<String> domains = getUserDomainListByTenantId(tenantId);
            userLambdaQueryWrapper.in(User::getDomain, domains);
            List<User> list = iUserRepository.list(userLambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(list)){
                filterUserIds = list.stream().map(User::getUserId).collect(Collectors.toList());
            } else {
                return PageResponse.of(new ArrayList<>(), 0, pageSize, pageNum);
            }
        }

        LambdaQueryWrapper<UserDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDepartment::getTenantId, tenantId);
        Long deptId = tenantUserQueryDTO.getDeptId();
        if (deptId != null && deptId > 0) {
            queryWrapper.eq(UserDepartment::getDeptId, deptId);
        }
        if (filterUser){
            queryWrapper.in(UserDepartment::getUserId, filterUserIds);
        }
        if (!tenantUserQueryDTO.getIsDeleted()){
            queryWrapper.eq(UserDepartment::getIsDeleted, 0);
        }

        Page<UserDepartment> iPage = new Page<>(pageNum, pageSize, true);
        iPage.addOrder(OrderItem.asc("deptPos"));
        Page<UserDepartment> pageRes = iUserDepartmentRepository.page(iPage, queryWrapper);
        if (pageRes != null && pageRes.getTotal() > 0) {
            List<UserDepartment> userDepartments = pageRes.getRecords();
            // 用户map
            List<Long> userIds = userDepartments.stream().map(UserDepartment::getUserId).collect(Collectors.toList());
            SingleResponse<Map<Long, UserInfoDTO>> userRes = userServiceImpl.queryMapByUserIds(userIds);
            Map<Long, UserInfoDTO> userMap = userRes.getData();
            // 部门map
            List<Long> deptIds = userDepartments.stream().filter(a -> (a.getIsDeleted().equals(0) && !a.getDeptId().equals(0L))).map(UserDepartment::getDeptId).distinct().collect(Collectors.toList());
            SingleResponse<Map<Long, String>> deptMapRes = iDepartmentRepository.queryDepartmentNameMap(tenantId, deptIds);
            Map<Long, String> deptMap = deptMapRes.getData();
            // 组装DTO
            List<TenantUserInfoDTO> list = new ArrayList<>();
            userDepartments.forEach(a -> {
                    UserInfoDTO userInfoDTO = userMap.get(a.getUserId());
                    if (userInfoDTO != null){
                        TenantUserInfoDTO tenantUserInfoDTO = TenantUserConverter.convertTo(userInfoDTO);
                        if (!tenantUserInfoDTO.getIsDeleted() && !a.getDeptId().equals(0L)){
                            tenantUserInfoDTO.setDeptName(deptMap.get(a.getDeptId()));
                            tenantUserInfoDTO.setDeptId(a.getDeptId());
                        }
                        tenantUserInfoDTO.setTenantId(tenantId);
                        list.add(tenantUserInfoDTO);
                    }
                }
            );
            return PageResponse.of(list, (int) pageRes.getTotal(), pageSize, pageNum);
        }
        return PageResponse.of(new ArrayList<>(), 0, pageSize, pageNum);
    }

    @Override
    public MultiResponse<DepartmentDTO> getAllDepartment(Long tenantId) {
        return iDepartmentRepository.getAllDepartment(tenantId);
    }

    @Override
    public MultiResponse<TenantUserInfoDTO> fuzzyQueryUser(Long tenantId, String uname, Boolean includeDeleted) {
        List<String> domains = getUserDomainListByTenantId(tenantId);
        if (CollectionUtils.isEmpty(domains)){
            return MultiResponse.of(new ArrayList<>());
        }
        MultiResponse<UserInfoDTO> res = userServiceImpl.fuzzyQueryByUserName(uname, domains, includeDeleted);
        if (!res.isSuccess()){
            return MultiResponse.buildFailure(res.getErrCode(),res.getErrMessage());
        }
        List<UserInfoDTO> userInfoDTOS = res.getData();
        if (CollectionUtils.isEmpty(userInfoDTOS)){
            return MultiResponse.of(new ArrayList<>());
        }
        // 用户map
        List<Long> userIds = userInfoDTOS.stream().map(UserInfoDTO::getUserId).toList();
        Map<Long, UserInfoDTO> userMap = userInfoDTOS.stream().collect(Collectors.toMap(UserInfoDTO::getUserId, u -> u));
        // 部门map
        LambdaQueryWrapper<UserDepartment> userDeptWrapper = new LambdaQueryWrapper<>();
        userDeptWrapper.eq(UserDepartment::getTenantId, tenantId)
                .in(UserDepartment::getUserId, userIds)
                .eq(UserDepartment::getIsDeleted, false);
        List<UserDepartment> userDepartments = iUserDepartmentRepository.list(userDeptWrapper);
        Map<Long, Long> userDeptMap = userDepartments.stream().collect(Collectors.toMap(UserDepartment::getUserId, UserDepartment::getDeptId));
        List<Long> deptIds = userDepartments.stream().filter(a -> (a.getIsDeleted().equals(0) && !a.getDeptId().equals(0L))).map(UserDepartment::getDeptId).distinct().collect(Collectors.toList());
        SingleResponse<Map<Long, String>> deptMapRes = iDepartmentRepository.queryDepartmentNameMap(tenantId, deptIds);
        Map<Long, String> deptMap = deptMapRes.getData();
        // 组装DTO
        List<TenantUserInfoDTO> list = new ArrayList<>();
        userInfoDTOS.forEach(a -> {
            TenantUserInfoDTO tenantUserInfoDTO = TenantUserConverter.convertTo(a);
            Long deptId = userDeptMap.get(a.getUserId());
            if (!tenantUserInfoDTO.getIsDeleted() && deptId != null && !deptId.equals(0L)){
                tenantUserInfoDTO.setDeptName(deptMap.get(deptId));
                tenantUserInfoDTO.setDeptId(deptId);
            } else {
                tenantUserInfoDTO.setDeptName("");
                tenantUserInfoDTO.setDeptId(null);
            }
            tenantUserInfoDTO.setTenantId(tenantId);
            list.add(tenantUserInfoDTO);
            }
        );
        return MultiResponse.of(list);
    }

    @Override
    public SingleResponse<Boolean> updateDepartment(DepartmentDTO departmentDTO) {
        return iDepartmentRepository.update(departmentDTO);
    }

    @Override
    public SingleResponse<Boolean> deleteDepartment(Long tenantId, Long deptId) {
        List<Long> deptIds = new ArrayList<>();
        deptIds.add(deptId);
        // 查所有子部门id
        MultiResponse<Long> subRes = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
        if (subRes.isSuccess()) {
            deptIds.addAll(subRes.getData());
        }
        LambdaQueryWrapper<UserDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDepartment::getTenantId, tenantId);
        queryWrapper.in(UserDepartment::getDeptId, deptIds);
        queryWrapper.eq(UserDepartment::getIsDeleted, 0);
        long count = iUserDepartmentRepository.count(queryWrapper);
        if (count > 0){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "请先移除该部门下包含的员工");
        }
        return iDepartmentRepository.delete(tenantId, deptId);
    }

    @Override
    public SingleResponse<Boolean> addDepartment(DepartmentDTO departmentDTO) {
        return iDepartmentRepository.add(departmentDTO);
    }

    @Override
    public SingleResponse<TenantUserInfoDTO> addUser(TenantUserRegisterDTO tenantUserRegisterDTO) {
        EnumTenantSessionDomainDTO tenantDomainDTO = tenantDomainCacheManager.getTenantDomainById(tenantUserRegisterDTO.getTenantId());
        if (tenantDomainDTO == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        if (tenantUserRegisterDTO.getDeptId() != null){
            SingleResponse<DepartmentDTO> departmentDTOSingleResponse = iDepartmentRepository.get(tenantUserRegisterDTO.getTenantId(), tenantUserRegisterDTO.getDeptId(), false);
            if (!departmentDTOSingleResponse.isSuccess() || departmentDTOSingleResponse.getData() == null){
                return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "部门不存在");
            }
        }
        // 注册账号
        CommonRegisterUserInfoDTO commonRegisterUserInfoDTO = new CommonRegisterUserInfoDTO();
        commonRegisterUserInfoDTO.setUnick(tenantUserRegisterDTO.getUnick());
        commonRegisterUserInfoDTO.setUname(tenantUserRegisterDTO.getUname());
        commonRegisterUserInfoDTO.setPassword(tenantUserRegisterDTO.getPassword());
        commonRegisterUserInfoDTO.setDomain(tenantDomainDTO.getDomain());
        commonRegisterUserInfoDTO.setStatus(tenantUserRegisterDTO.getIsVirtual()?2:0);

        Map<String, Object> extraInfo = new HashMap<>();
        if (StringUtils.isNotEmpty(tenantUserRegisterDTO.getJobNumber())){
            extraInfo.put("jobNumber", tenantUserRegisterDTO.getJobNumber());
        }
        if (StringUtils.isNotEmpty(tenantUserRegisterDTO.getEmail())){
            extraInfo.put("email", tenantUserRegisterDTO.getEmail());
        }
//        if (StringUtils.isNotEmpty(tenantUserRegisterDTO.getMobile())){
//            extraInfo.put("mobile", tenantUserRegisterDTO.getMobile());
//        }
        commonRegisterUserInfoDTO.setUsersExtraInfo(extraInfo);
        commonRegisterUserInfoDTO.setDeviceId("");

        commonRegisterUserInfoDTO.setAvatar(tenantUserRegisterDTO.getAvatar());

        SingleResponse<UserInfoDTO> registerRes = registerServiceImpl.register(commonRegisterUserInfoDTO);
        if (!registerRes.isSuccess()){
            return SingleResponse.buildFailure(registerRes.getErrCode(),registerRes.getErrMessage());
        }
        UserInfoDTO userInfoDTO = registerRes.getData();
        TenantUserInfoDTO tenantUserInfoDTO = BeanUtil.copyProperties(userInfoDTO, TenantUserInfoDTO.class);
        tenantUserInfoDTO.setTenantId(tenantUserRegisterDTO.getTenantId());

        if (tenantUserRegisterDTO.getDeptId() != null){
            Integer deptPos = iDepartmentRepository.getDeptPos(tenantUserRegisterDTO.getTenantId());
            // 关联部门
            UserDepartment userDepartment = new UserDepartment();
            userDepartment.setTenantId(tenantDomainDTO.getTenantId());
            userDepartment.setUserId(userInfoDTO.getUserId());
            userDepartment.setDeptId(tenantUserRegisterDTO.getDeptId());
            userDepartment.setDeptPos(deptPos);
            userDepartment.setCreated(CommonUtil.getCurrentSeconds());
            userDepartment.setUpdated(CommonUtil.getCurrentSeconds());
            boolean res = iUserDepartmentRepository.save(userDepartment);
            if (!res){
                return SingleResponse.buildFailure(GlobalResponseCodeEnum.FAIL.getCode(), "部门绑定失败");
            }
            tenantUserInfoDTO.setDeptId(userDepartment.getDeptId());
        }
        return SingleResponse.buildSuccess(tenantUserInfoDTO);
    }

    @Override
    public SingleResponse<TenantUserInfoDTO> getUserByUserId(Long tenantId, Long userId) {
        SingleResponse<TenantUserInfoDTO> response = userTenantServiceImpl.queryByUserId(tenantId, userId);
        if (!response.isSuccess()){
            return response;
        }
        TenantUserInfoDTO tenantUserInfoDTO = response.getData();
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantUserInfoDTO.getTenantId());
        SingleResponse<UserMobileInfoDTO> response1 = mobileServiceImpl.queryMobileInfoByUserId(tenantUserInfoDTO.getUserId(), tenantDomain.getDomain());
        if (response1.getData()!= null){
            tenantUserInfoDTO.setMobile(response1.getData().getMobile());
        }
        return SingleResponse.buildSuccess(tenantUserInfoDTO);
    }

    @Override
    public SingleResponse<Boolean> updateUser(TenantUserUpdateDTO tenantUserUpdateDTO) {
        EnumTenantSessionDomainDTO tenantDomainDTO = tenantDomainCacheManager.getTenantDomainById(tenantUserUpdateDTO.getTenantId());
        if (tenantDomainDTO == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        SingleResponse<TenantUserInfoDTO> userRes = getUserByUserId(tenantUserUpdateDTO.getTenantId(), tenantUserUpdateDTO.getUserId());
        if (!userRes.isSuccess()){
            return SingleResponse.buildFailure(userRes.getErrCode(),userRes.getErrMessage());
        }
        SingleResponse<DepartmentDTO> departmentDTOSingleResponse = iDepartmentRepository.get(tenantUserUpdateDTO.getTenantId(), tenantUserUpdateDTO.getDeptId(), false);
        if (!departmentDTOSingleResponse.isSuccess() || departmentDTOSingleResponse.getData() == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "部门不存在");
        }
        TenantUserInfoDTO oldUserInfoDTO = userRes.getData();
        // 更新基础信息
        UserInfoDTO updateUserInfoDTO = new UserInfoDTO();
        updateUserInfoDTO.setUserId(oldUserInfoDTO.getUserId());
        // uname不可编辑
//        updateUserInfoDTO.setUname(tenantUserUpdateDTO.getUname());
        updateUserInfoDTO.setUnick(tenantUserUpdateDTO.getUnick());
        updateUserInfoDTO.setAvatar(tenantUserUpdateDTO.getAvatar());
        Map<String, Object> extraInfo = new HashMap<>();
        if (tenantUserUpdateDTO.getEmail() != null){
            extraInfo.put("email", tenantUserUpdateDTO.getEmail());
        }
        if (tenantUserUpdateDTO.getJobNumber() != null){
            extraInfo.put("jobNumber", tenantUserUpdateDTO.getJobNumber());
        }
        if (tenantUserUpdateDTO.getPosition() != null){
            extraInfo.put("position", tenantUserUpdateDTO.getPosition());
        }
        // 更新手机号
        if (StringUtils.isNotEmpty(tenantUserUpdateDTO.getMobile())){
            SingleResponse<UserMobileInfoDTO> existMobileRes = mobileServiceImpl.queryMobileInfoByUserId(tenantUserUpdateDTO.getUserId(), tenantDomainDTO.getDomain());
            UserMobileInfoDTO existMobile = existMobileRes.getData();
            if (existMobile != null && !tenantUserUpdateDTO.getMobile().equals(existMobile.getMobile())){
                mobileServiceImpl.unbindMobile(existMobile.getUserId(), existMobile.getMobile(), existMobile.getAreaCode(), existMobile.getDomain());
            }
            if (existMobile == null || !tenantUserUpdateDTO.getMobile().equals(existMobile.getMobile())){
                SingleResponse<Boolean> mobileRes = mobileServiceImpl.setMobile(tenantUserUpdateDTO.getUserId(), tenantUserUpdateDTO.getMobile(), "86", tenantDomainDTO.getDomain(), 1, null);
                if (!mobileRes.isSuccess()){
                    return SingleResponse.buildFailure(mobileRes.getErrCode(),mobileRes.getErrMessage());
                }
            }
        } else {
            SingleResponse<UserMobileInfoDTO> existMobileRes = mobileServiceImpl.queryMobileInfoByUserId(tenantUserUpdateDTO.getUserId(), tenantDomainDTO.getDomain());
            if (existMobileRes.getData() != null){
                UserMobileInfoDTO existMobile = existMobileRes.getData();
                mobileServiceImpl.unbindMobile(existMobile.getUserId(), existMobile.getMobile(), existMobile.getAreaCode(), existMobile.getDomain());
            }
        }
//        if (tenantUserUpdateDTO.getMobile() != null){
//            extraInfo.put("mobile", tenantUserUpdateDTO.getMobile());
//        }
        updateUserInfoDTO.setExtraInfo(extraInfo);
        SingleResponse<Boolean> res = userServiceImpl.updateUserInfo(updateUserInfoDTO);
        if (!res.isSuccess()){
            return SingleResponse.buildFailure(res.getErrCode(),res.getErrMessage());
        }
        // 更新是否虚拟账号
        if (!tenantUserUpdateDTO.getIsVirtual().equals(oldUserInfoDTO.getIsVirtual())){
            userServiceImpl.updateStatus(oldUserInfoDTO.getUserId(), USER_STATUS_NO_LOGIN, tenantUserUpdateDTO.getIsVirtual());
        }
        // 已删除账号恢复
        if (oldUserInfoDTO.getStatusList().contains(USER_STATUS_DELETED)){
            userServiceImpl.updateStatus(oldUserInfoDTO.getUserId(), USER_STATUS_DELETED, false);
        }
        // 更新部门
        if (!tenantUserUpdateDTO.getDeptId().equals(oldUserInfoDTO.getDeptId())){
            LambdaQueryWrapper<UserDepartment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserDepartment::getTenantId, tenantUserUpdateDTO.getTenantId());
            queryWrapper.eq(UserDepartment::getUserId, tenantUserUpdateDTO.getUserId());

            // 部门排序
            Integer deptPos = iDepartmentRepository.getDeptPos(tenantUserUpdateDTO.getDeptId());

            UserDepartment newUserDepartment = new UserDepartment();
            newUserDepartment.setUserId(tenantUserUpdateDTO.getUserId());
            newUserDepartment.setIsDeleted(0);
            newUserDepartment.setTenantId(tenantUserUpdateDTO.getTenantId());
            newUserDepartment.setDeptId(tenantUserUpdateDTO.getDeptId());
            newUserDepartment.setDeptPos(deptPos);
            newUserDepartment.setUpdated(CommonUtil.getCurrentSeconds());
            iUserDepartmentRepository.update(newUserDepartment ,queryWrapper);

        }
        return SingleResponse.buildSuccess(true);
    }

    @Override
    public SingleResponse<Boolean> deleteUser(Long tenantId, Long userId) {
        SingleResponse<TenantUserInfoDTO> res = getUserByUserId(tenantId, userId);
        if (!res.isSuccess()){
            return SingleResponse.buildFailure(res.getErrCode(), res.getErrMessage());
        }
        userServiceImpl.updateStatus(userId, USER_STATUS_DELETED, true);
        sessionServiceImpl.clearSession(userId);

        LambdaQueryWrapper<UserDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDepartment::getTenantId, tenantId);
        queryWrapper.eq(UserDepartment::getUserId, userId);

        UserDepartment newUserDepartment = new UserDepartment();
        newUserDepartment.setIsDeleted(1);
        newUserDepartment.setUpdated(CommonUtil.getCurrentSeconds());
        iUserDepartmentRepository.update(newUserDepartment, queryWrapper);

        LambdaQueryWrapper<UserMobile> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(UserMobile::getUserId, userId);
        iUserMobileRepository.remove(queryWrapper2);
        return SingleResponse.buildSuccess(true);
    }

    @Override
    public SingleResponse<Boolean> bindApp(Long tenantId, Long userId, String appId) {
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);
        if (tenantDomain == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "租户不存在");
        }

        LambdaQueryWrapper<AppAuthTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppAuthTemplate::getAppId, appId);
        AppAuthTemplate appAuthTemplate = iAppAuthTemplateRepository.getOne(wrapper);
        if (appAuthTemplate == null){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "APPID不存在");
        }

        SingleResponse<Boolean> res = iUserAuthRepository.copyAuthTemplate(tenantId, userId, appAuthTemplate.getAuthTenantId(), appAuthTemplate.getAuthPlatformId());
        if (!res.isSuccess()){
            log.warn("【租户】初始化权限 tenantId={} {}",tenantId, res.getErrMessage());
            return SingleResponse.buildFailure(res.getErrCode(), res.getErrMessage());
        }
        log.info("【租户】初始化权限模板成功 tenantId={} userId={}",tenantId, userId);
        EnumAppDomainAuth enumAppDomainAuth = new EnumAppDomainAuth();
        enumAppDomainAuth.setAppId(appAuthTemplate.getTemplateAppId());
        enumAppDomainAuth.setTenantId(tenantId);
        enumAppDomainAuth.setDomain(tenantDomain.getDomain());
        enumAppDomainAuth.setAuthApp(appAuthTemplate.getAuthApp() + tenantId);
        enumAppDomainAuth.setAuthSecret(appAuthTemplate.getAuthSecret() + tenantId);
        boolean res1 = iEnumAppDomainAuthRepository.save(enumAppDomainAuth);
        if (!res1){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.FAIL);
        }
        log.info("【租户】绑定应用权限成功 tenantId={} appId={}",tenantId, enumAppDomainAuth.getAppId());
        return SingleResponse.buildSuccess(true);
    }

    private List<String> getUserDomainListByTenantId(Long tenantId) {
        List<String> list = new ArrayList<>();
        LambdaQueryWrapper<EnumTenantDomain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnumTenantDomain::getTenantId, tenantId);
//        queryWrapper.eq(EnumTenantDomain::getIsDeleted, 0);
        List<EnumTenantDomain> enumTenantDomainList = iEnumTenantDomainRepository.list(queryWrapper);
        if (!CollectionUtils.isEmpty(enumTenantDomainList)){
            list = enumTenantDomainList.stream().map(EnumTenantDomain::getDomain).collect(Collectors.toList());
        }
        return list;
    }
}
