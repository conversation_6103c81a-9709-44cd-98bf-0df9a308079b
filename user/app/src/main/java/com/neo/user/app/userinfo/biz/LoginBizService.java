package com.neo.user.app.userinfo.biz;

import com.alibaba.fastjson.JSONObject;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.common.utils.IdConvertor;
import com.neo.session.Constants;
import com.neo.user.app.tennant.dto.TenantUserLoginLogDTO;
import com.neo.user.app.tennant.service.QiyeweixinBizService;
import com.neo.user.app.userinfo.biz.dto.CookieItem;
import com.neo.user.app.userinfo.biz.dto.LoginByEmailReqDTO;
import com.neo.user.app.userinfo.biz.dto.LoginItem;
import com.neo.user.app.userinfo.converter.UsersConverter;
import com.neo.user.app.userinfo.handler.RegisterBizHandler;
import com.neo.user.app.userinfo.impl.MobileServiceImpl;
import com.neo.user.app.userinfo.impl.PasswordServiceImpl;
import com.neo.user.app.userinfo.impl.RegisterServiceImpl;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.app.verify.impl.SessionServiceImpl;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.userinfo.dto.*;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.client.enums.UserStatusEnum;
import com.neo.user.domain.entity.User;
import com.neo.user.domain.gateway.cache.EnumTenantDomainCacheService;
import com.neo.user.domain.gateway.cache.UserCacheService;
import com.neo.user.domain.gateway.dto.TenantThirdPartyDTO;
import com.neo.user.domain.gateway.dto.TenantThirdPartySessionKeyInfoDTO;
import com.neo.user.domain.gateway.sendmsg.SendMessageService;
import com.neo.user.domain.gateway.thirdparty.TenantSaasService;
import com.neo.user.domain.gateway.thirdparty.ThirdPartyService;
import com.neo.user.domain.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalTime;
import org.joda.time.Seconds;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.neo.user.client.userinfo.Constants.Constants.USER_STATUS_DELETED;
import static com.neo.user.domain.constants.UserConstants.*;

@Service
@Slf4j
public class LoginBizService {

    private static Pattern CHINENES_MOBILE_PATTERN = Pattern.compile("^1[0-9]{10}$");

    private static Pattern MOBILE_PATTERN = Pattern.compile("^[0-9]{7,12}$");


    @Value("${neo.application.env}")
    private String env;

    @Value("${user.tenant.enable}")
    private Boolean tenantEnable;

    @Autowired
    private MobileServiceImpl mobileServiceImpl;

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private UserCacheService userCacheService;

    @Autowired
    private RegisterBizHandler registerBizHandler;

    @Autowired
    private RegisterServiceImpl registerServiceImpl;

    @Autowired
    private SessionServiceImpl sessionServiceImpl;

    @Autowired
    private PasswordServiceImpl passwordServiceImpl;

    @Autowired
    private ThirdPartyService thirdPartyService;

    @Autowired
    private EnumTenantDomainCacheService tenantDomainCacheManager;

    @Autowired
    private PasswordBizService passwordBizService;

    @Autowired
    private UserLoginLogBizService userLoginLogBizService;

    @Autowired
    private TenantSaasService tenantSaasService;

    @Autowired
    private QiyeweixinBizService qiyeweixinBizService;

    public SingleResponse<LoginItem> loginByUnameAndPwd(Map<String, String> requestData) {
        SingleResponse<LoginItem> singleResponse = new SingleResponse<>();
        String password = requestData.get("password");
        String uname = requestData.get("uname");
//        String areaCode = requestData.get("areaCode");
        String domain = requestData.get("domain");
        String deviceId = requestData.get("deviceId");
        Boolean userExists = false;
        UserInfoDTO userInfoDTO = null;
//        if (isMobileNumber(uname)) {
//            SingleResponse<UserInfoDTO> userInfoResponse = userServiceImpl.queryByUserMobile(uname, areaCode, domain);
//            if (userInfoResponse.isSuccess() && userInfoResponse.getData() != null) {
//                userExists = true;
//                userInfoDTO = userInfoResponse.getData();
//            }
//        } else
        if (!tenantEnable){
            SingleResponse<UserInfoDTO> userInfoResponse2 = userServiceImpl.queryByUserName(uname, domain);
            if (userInfoResponse2.isSuccess() && userInfoResponse2.getData() != null) {
                userExists = true;
                userInfoDTO = userInfoResponse2.getData();
            }
        } else {
            MultiResponse<UserInfoDTO> userInfoResponse3 = userServiceImpl.queryAllUserByName(uname);
            if (userInfoResponse3.isSuccess() && !CollectionUtils.isEmpty(userInfoResponse3.getData())) {
                userInfoDTO = userInfoResponse3.getData().get(0);
                domain = userInfoDTO.getDomain();
                userExists = true;
            }
        }
        if (!userExists) {
            singleResponse.setErrCode(UserConstants.ERROR_USER_NOT_EXIST);
            singleResponse.setErrMessage("用户名或密码错误，请重新登录");
            return singleResponse;
        }

        EnumTenantSessionDomainDTO bizDomain = tenantDomainCacheManager.getSessionDomainByDomain(domain);
        // 日志
        TenantUserLoginLogDTO tenantUserLoginLogDTO = new TenantUserLoginLogDTO();
        tenantUserLoginLogDTO.setUserId(userInfoDTO.getUserId());
        tenantUserLoginLogDTO.setTenantId(bizDomain.getTenantId());
        tenantUserLoginLogDTO.setUname(userInfoDTO.getUname());
        tenantUserLoginLogDTO.setUnick(userInfoDTO.getUnick());
        tenantUserLoginLogDTO.setEventType(EVENT_TYPE_LOGIN);

        if (CommonUtil.checkUserStatus(userInfoDTO.getStatusList(), UserStatusEnum.IS_DELETED)) {
            singleResponse.setErrCode(UserConstants.ERROR_USER_NOT_EXIST);
            singleResponse.setErrMessage("用户名或密码错误，请重新登录");
            return singleResponse;
        }
        if (CommonUtil.checkUserStatus(userInfoDTO.getStatusList(), UserStatusEnum.NO_LOGIN)) {
            singleResponse.setErrCode(UserConstants.LOGIN_ERROR_FORBIDDEN);
            singleResponse.setErrMessage("当前用户禁止登录");

            //记录日志
            tenantUserLoginLogDTO.setDesc(EVENT_TYPE_LOGIN_PWD + "，当前用户禁止登录");
            tenantUserLoginLogDTO.setResult(RESULT_FAIL);
            userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);
            return singleResponse;
        }

        if (passwordBizService.checkPasswordErrorCount(userInfoDTO.getUserId())) {
            singleResponse.setErrCode(UserConstants.LOGIN_ERROR_FORBIDDEN);
            singleResponse.setErrMessage("密码错误次数过多，账号锁定1小时");
            //记录日志
            tenantUserLoginLogDTO.setDesc(EVENT_TYPE_LOGIN_PWD + "，密码错误次数过多");
            tenantUserLoginLogDTO.setResult(RESULT_FAIL);
            userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);
            return singleResponse;
        }

        SingleResponse<Boolean> checkResponse = passwordServiceImpl.checkPassword(userInfoDTO.getUserId(), password, domain);
        if (!checkResponse.isSuccess() || !checkResponse.getData()) {
            // 密码错误次数+1
            Long errorCount = passwordBizService.incrPasswordErrorCount(userInfoDTO.getUserId());
            if (errorCount != null && errorCount >= 5){
                singleResponse.setErrCode(UserConstants.LOGIN_ERROR_FORBIDDEN);
                singleResponse.setErrMessage("密码错误次数过多，账号锁定1小时");
                //记录日志
                tenantUserLoginLogDTO.setDesc(EVENT_TYPE_LOGIN_PWD + "，密码错误次数过多");
            } else {
                singleResponse.setErrCode(UserConstants.ERROR_USER_NOT_EXIST);
                singleResponse.setErrMessage("用户名或密码错误，请重新登录");
                //记录日志
                tenantUserLoginLogDTO.setDesc(EVENT_TYPE_LOGIN_PWD + "，用户名或密码错误");
            }
            tenantUserLoginLogDTO.setResult(RESULT_FAIL);
            userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);
            return singleResponse;
        }
        passwordBizService.clearPasswordErrorCount(userInfoDTO.getUserId());
        LoginItem loginItem = new LoginItem();
        SingleResponse<String> signRes = sessionServiceImpl.getSign(userInfoDTO.getUserId(), deviceId, domain);
        loginItem.setSign(signRes.getData());
        loginItem.setUname(userInfoDTO.getUname());
        loginItem.setUid(IdConvertor.idToUrl(userInfoDTO.getUserId()));
        loginItem.setDomain(domain);
        List<CookieItem> cookieItems = new ArrayList<>();
        EnumTenantSessionDomainDTO enumTenantSessionDomainDTO = tenantDomainCacheManager.getSessionDomainByDomain(domain);
        cookieItems.add(new CookieItem(enumTenantSessionDomainDTO.getSessionDomain(), Constants.COOKIE_SIGN_NAME, signRes.getData()));
        if (tenantEnable){
            cookieItems.add(new CookieItem(enumTenantSessionDomainDTO.getSessionDomain(),Constants.COOKIE_SIGN_TENANT , enumTenantSessionDomainDTO.getDomain()));
        }
        loginItem.setCookies(cookieItems);
        singleResponse.setSuccess(Boolean.TRUE);
        singleResponse.setData(loginItem);
        // 记录日志
        tenantUserLoginLogDTO.setDesc(EVENT_TYPE_LOGIN_PWD);
        tenantUserLoginLogDTO.setResult(RESULT_SUCCESS);
        userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);
        return singleResponse;
    }

    public SingleResponse<Boolean> logout(Map<String, String> parameter) {
        SingleResponse<Boolean> singleResponse = new SingleResponse<>();
        String sign = parameter.get("sign");
        SingleResponse<Boolean> result = sessionServiceImpl.clearCurrentSession(sign);
        if (result.getData()) {
            singleResponse.setSuccess(Boolean.TRUE);
            singleResponse.setData(result.getData());
        } else {
            singleResponse.setErrCode(UserConstants.ERROR_USER_LOGOUT);
            singleResponse.setErrMessage("logout error");
        }
        return singleResponse;
    }

    public static boolean isMobileNumber(String mobile) {
        if (StringUtils.isEmpty(mobile))
            return false;
        Matcher matcher = MOBILE_PATTERN.matcher(mobile);
        return matcher.matches();
    }

    public static boolean isMobileNumber(String areaCode, String mobile) {
        if (areaCode.endsWith("86")) {
            if (StringUtils.isEmpty(mobile))
                return false;
            Matcher matcher = CHINENES_MOBILE_PATTERN.matcher(mobile);
            return matcher.matches();
        } else {
            return isMobileNumber(mobile);
        }
    }

    /**
     * 生成6位数字验证码<p>
     *
     * @return
     */
    private String getCode() {
        int r = (int) Math.floor(999999 * Math.random());
        return String.format("%06d", r);
    }

    public SingleResponse<LoginItem> qiyeweixinOauthCallBack(Map<String, String> requestData, String type) {
        SingleResponse<LoginItem> singleResponse = new SingleResponse<>();
        singleResponse.setSuccess(Boolean.FALSE);
        // 1、获取请求参数
        String code = requestData.get("code");
        String appId = requestData.get("appId");
        String deviceId = requestData.getOrDefault("deviceId", "");

        // 2、获取用户授权token
        TenantThirdPartySessionKeyInfoDTO thirdPartySessionKeyInfoDTO = tenantSaasService.getUserAccessToken(appId, code, type);
        if (null == thirdPartySessionKeyInfoDTO || StringUtils.isEmpty(thirdPartySessionKeyInfoDTO.getCorpId())
                || StringUtils.isBlank(thirdPartySessionKeyInfoDTO.getOpenId())
                || StringUtils.isBlank(thirdPartySessionKeyInfoDTO.getUnionId())) {
            log.error("[loginByThirdParty][getUserAccessToken failure][requestData={},thirdPartySessionKeyInfoDTO={}]",
                    requestData, thirdPartySessionKeyInfoDTO);
            singleResponse.setErrCode(UserConstants.THIRD_PARTY_LOGIN_ERROR);
            singleResponse.setErrMessage("第三方登录失败");
            return singleResponse;
        }
        Long tenantId = tenantSaasService.getTenantIdByCorpId(appId, thirdPartySessionKeyInfoDTO.getCorpId());
        if (tenantId == null){
            // 租户不存在 或 租户未授权该appId应用
            log.error("[loginByThirdParty][getTenantIdByCorpId failure][requestData={}, tenantId is null]",
                    requestData);
            singleResponse.setErrCode(UserConstants.THIRD_PARTY_LOGIN_ERROR);
            singleResponse.setErrMessage("企业未授权该应用");
            return singleResponse;
        }
        EnumTenantSessionDomainDTO tenantDomain = tenantDomainCacheManager.getTenantDomainById(tenantId);

//        SingleResponse<ThirdPartyInfoDTO> thirdPartyInfoDTOSingleResponse = userServiceImpl.queryThirdPartyInfoByThirdIdAndAppId(thirdPartySessionKeyInfoDTO.getUnionId(), appId, ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
        SingleResponse<UserInfoDTO> thirdPartyInfoDTOSingleResponse = userServiceImpl.queryByUserThirdParty(thirdPartySessionKeyInfoDTO.getUnionId(), ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode(), tenantDomain.getDomain());
        if (!thirdPartyInfoDTOSingleResponse.isSuccess()) {
            singleResponse.setErrCode(UserConstants.THIRD_PARTY_LOGIN_ERROR);
            singleResponse.setErrMessage("第三方登录失败");
            return singleResponse;
        }
        UserInfoDTO userInfoDTO = null;
        Long userId = null;
        if (thirdPartyInfoDTOSingleResponse.getData() == null){
            TenantUserInfoDTO tenantUserInfoDTO = null;
            if (!StringUtils.isEmpty(thirdPartySessionKeyInfoDTO.getSessionKey())){
                // TODO 注册新用户
                String tenantUserPrivateInfo = tenantSaasService.getTenantUserPrivateInfo(appId, thirdPartySessionKeyInfoDTO.getSessionKey());
                if (StringUtils.isEmpty(tenantUserPrivateInfo)){
                    return null;
                }
                JSONObject jsonObject = JSONObject.parseObject(tenantUserPrivateInfo);
                tenantUserInfoDTO = processRegist(appId, tenantId, jsonObject.getString("corpid"), jsonObject.getString("userid"),
                        jsonObject.getString("name"), jsonObject.getString("open_userid"));
            } else {
                tenantUserInfoDTO = processRegist(appId, tenantId, thirdPartySessionKeyInfoDTO.getCorpId(), thirdPartySessionKeyInfoDTO.getOpenId(),
                        thirdPartySessionKeyInfoDTO.getOpenId(), thirdPartySessionKeyInfoDTO.getUnionId());
            }
            if (tenantUserInfoDTO == null){
                singleResponse.setErrCode(UserConstants.THIRD_PARTY_LOGIN_ERROR);
                singleResponse.setErrMessage("第三方登录失败");
                return singleResponse;
            }
            userId = tenantUserInfoDTO.getUserId();
        }else {
            userId = thirdPartyInfoDTOSingleResponse.getData().getUserId();
        }

        SingleResponse<UserInfoDTO> userRes = userServiceImpl.queryUserByUserId(userId);
        if (!userRes.isSuccess() || userRes.getData() == null){
            singleResponse.setErrCode(UserConstants.THIRD_PARTY_LOGIN_ERROR);
            singleResponse.setErrMessage("第三方登录失败");
            return singleResponse;
        }
        userInfoDTO = userRes.getData();
        String domain = userInfoDTO.getDomain();
        EnumTenantSessionDomainDTO bizDomain = tenantDomainCacheManager.getSessionDomainByDomain(domain);

        LoginItem loginItem = new LoginItem();
        SingleResponse<String> signRes = sessionServiceImpl.getSign(userInfoDTO.getUserId(), deviceId, domain);
        loginItem.setSign(signRes.getData());
        loginItem.setUname(userInfoDTO.getUname());
        loginItem.setDomain(domain);
        List<CookieItem> cookieItems = new ArrayList<>();
        cookieItems.add(new CookieItem(bizDomain.getSessionDomain(), Constants.COOKIE_SIGN_NAME, signRes.getData()));
        if (tenantEnable){
            cookieItems.add(new CookieItem(bizDomain.getSessionDomain(),Constants.COOKIE_SIGN_TENANT , bizDomain.getDomain()));
        }
        loginItem.setCookies(cookieItems);
        singleResponse.setSuccess(Boolean.TRUE);
        singleResponse.setData(loginItem);

        //记录日志
        TenantUserLoginLogDTO tenantUserLoginLogDTO = new TenantUserLoginLogDTO();

        tenantUserLoginLogDTO.setUserId(userInfoDTO.getUserId());
        tenantUserLoginLogDTO.setTenantId(bizDomain.getTenantId());
        tenantUserLoginLogDTO.setUname(userInfoDTO.getUname());
        tenantUserLoginLogDTO.setUnick(userInfoDTO.getUnick());
        tenantUserLoginLogDTO.setEventType(EVENT_TYPE_LOGIN);
        tenantUserLoginLogDTO.setDesc(EVENT_TYPE_LOGIN_QIYEWEIXIN);
        tenantUserLoginLogDTO.setResult(RESULT_SUCCESS);
        userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);
        return singleResponse;
    }

    private TenantUserInfoDTO processRegist(String appId, Long tenantId, String corpId, String thirdUserId, String thirdUserName, String thirdUserOpenId){
        TenantThirdPartyDTO tenantThirdPartyDTO = new TenantThirdPartyDTO();
        tenantThirdPartyDTO.setCorpId(corpId);
        tenantThirdPartyDTO.setAppId(appId);
        tenantThirdPartyDTO.setTenantId(tenantId);
        tenantThirdPartyDTO.setThirdUserId(thirdUserId);
        tenantThirdPartyDTO.setThirdUserName(thirdUserName);
        tenantThirdPartyDTO.setThirdUserOpenId(thirdUserOpenId);
        TenantUserInfoDTO tenantUserInfoDTO = qiyeweixinBizService.addTenantUser(appId, tenantThirdPartyDTO);
        return tenantUserInfoDTO;
    }

}
