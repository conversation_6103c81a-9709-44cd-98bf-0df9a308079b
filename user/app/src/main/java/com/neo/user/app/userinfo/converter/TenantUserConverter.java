package com.neo.user.app.userinfo.converter;

import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;

public class TenantUserConverter {

    public static TenantUserInfoDTO convertTo(UserInfoDTO userInfo) {
        TenantUserInfoDTO tenantUserInfo = new TenantUserInfoDTO();
        tenantUserInfo.setUserId(userInfo.getUserId());
        tenantUserInfo.setUname(userInfo.getUname());
        tenantUserInfo.setUnick(userInfo.getUnick());
        tenantUserInfo.setAvatar(userInfo.getAvatar());
        tenantUserInfo.setGender(userInfo.getGender());
        tenantUserInfo.setExtraInfo(userInfo.getExtraInfo());
        tenantUserInfo.setRegisterInfo(userInfo.getRegisterInfo());
        tenantUserInfo.setDomain(userInfo.getDomain());
        tenantUserInfo.setStatusList(userInfo.getStatusList());
        tenantUserInfo.setCreated(userInfo.getCreated());
        tenantUserInfo.setUpdated(userInfo.getUpdated());
        return tenantUserInfo;
    }
}
