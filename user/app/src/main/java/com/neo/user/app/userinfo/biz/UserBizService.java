package com.neo.user.app.userinfo.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.api.MultiResponse;
import com.neo.user.app.userinfo.biz.dto.ThirdBindDTO;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.domain.entity.UserThirdParty;
import com.neo.user.domain.gateway.IUserThirdPartyRepository;
import com.neo.user.domain.gateway.cache.EnumTenantDomainCacheService;
import com.neo.user.domain.gateway.cache.EnumThirdPartyAppInfoCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class UserBizService {

    @Autowired
    private IUserThirdPartyRepository userThirdPartyRepository;

    /**
     * 获取用户第三方绑定信息
     * @param appId
     * @param userId
     * @return
     */
    public MultiResponse<ThirdBindDTO> getThirdBindInfo(String appId, Long userId) {
        List<ThirdBindDTO> list = new ArrayList<>();
        LambdaQueryWrapper<UserThirdParty> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserThirdParty::getAppId, appId);
        queryWrapper.eq(UserThirdParty::getUserId, userId);
        List<UserThirdParty> thirdList = userThirdPartyRepository.list(queryWrapper);
        if (!CollectionUtils.isEmpty(thirdList)){
            thirdList.forEach(a -> {
                ThirdBindDTO thirdBindDTO = new ThirdBindDTO();
                thirdBindDTO.setAppId(appId);
                thirdBindDTO.setHasBind(true);
                thirdBindDTO.setThirdPlatform(ThirdPartyEnum.getThirdPartyByCode(a.getThirdType()).getMsg());
                list.add(thirdBindDTO);
            });
        }
        return MultiResponse.of(list);
    }
}
