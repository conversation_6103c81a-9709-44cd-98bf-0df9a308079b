package com.neo.user.app.userinfo.converter;

import com.alibaba.fastjson.JSON;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import com.neo.user.domain.entity.UserMobile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class UserMobileConverter {

    public static UserMobileInfoDTO doToVO(final UserMobile userMobile) {

        if (null == userMobile) {
            return null;
        }
        UserMobileInfoDTO userMobileInfoDTO = new UserMobileInfoDTO();

        userMobileInfoDTO.setUserId(userMobile.getUserId());
        userMobileInfoDTO.setAreaCode(userMobile.getAreaCode());
        userMobileInfoDTO.setMobile(userMobile.getMobile());
        userMobileInfoDTO.setDomain(userMobile.getDomain());
        userMobileInfoDTO.setStatus(userMobile.getStatus());
        if (!StringUtils.isBlank(userMobile.getExtra())) {
            try {
                userMobileInfoDTO.setExtra(JSON.parseObject(userMobile.getExtra(), Map.class));
            } catch (Exception e) {
                log.error("[UsersMobileConverter.doToVO][JSON.parseObject exception][extra=" + userMobile.getExtra()
                        + "]", e);
                Map<String, Object> originData = new HashMap<String, Object>();
                originData.put("originData", userMobile.getExtra());
                userMobileInfoDTO.setExtra(originData);
            }
        }
        userMobileInfoDTO.setCreated(userMobile.getCreated());
        userMobileInfoDTO.setUpdated(userMobile.getUpdated());

        return userMobileInfoDTO;
    }

    public static List<UserMobileInfoDTO> doToVOList(final List<UserMobile> userMobileList) {

        if (CollectionUtils.isEmpty(userMobileList)) {
            return null;
        }
        List<UserMobileInfoDTO> list = new ArrayList<>(userMobileList.size());
        for (UserMobile mobileDO : userMobileList) {
            UserMobileInfoDTO mobileInfo = doToVO(mobileDO);
            if (mobileInfo == null) {
                continue;
            }
            list.add(mobileInfo);
        }
        return list;
    }
}
