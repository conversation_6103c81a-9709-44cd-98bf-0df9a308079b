package com.neo.user.app.userinfo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.userinfo.converter.UsersConverter;
import com.neo.user.app.userinfo.handler.RegisterBizHandler;
import com.neo.user.client.userinfo.api.RegisterService;
import com.neo.user.client.userinfo.dto.CommonRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.MobileRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.ThirdPartyRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.domain.entity.User;
import com.neo.user.domain.entity.UserMobile;
import com.neo.user.domain.entity.UserThirdParty;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.domain.gateway.IUserMobileRepository;
import com.neo.user.domain.gateway.IUserThirdPartyRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Slf4j
@Service
public class RegisterServiceImpl implements RegisterService {

    @Autowired
    private IUserMobileRepository iUserMobileRepository;

    @Autowired
    private IUserThirdPartyRepository iUserThirdPartyRepository;

    @Autowired
    private RegisterBizHandler registerBizHandler;
    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    /**
     * 手机号码注册用户
     *
     * @param mobileRegisterUserInfo 手机号注册信息
     * @return 注册成功返回 {@link UserInfoDTO} 对象
     */
    @Override
    public SingleResponse<UserInfoDTO> registerByMobile(MobileRegisterUserInfoDTO mobileRegisterUserInfo) {
        SingleResponse<UserInfoDTO> result = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(mobileRegisterUserInfo.getUname()) || StringUtils.isEmpty(mobileRegisterUserInfo.getMobile())
                || StringUtils.isEmpty(mobileRegisterUserInfo.getAreaCode())
                || null == tenantDomainCacheManager.getSessionDomainByDomain(mobileRegisterUserInfo.getDomain())) {
            log.error("[RegisterServiceImpl.registerByMobile][invalid params][mobileRegisterUserInfo={}]",
                    mobileRegisterUserInfo);
            result.setErrCode(UserConstants.ERROR_PARAM);
            result.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return result;
        }

        try {
            // 检查手机号是否已经绑定
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", mobileRegisterUserInfo.getMobile());
            queryWrapper.eq("areaCode", mobileRegisterUserInfo.getAreaCode());
            queryWrapper.eq("domain", mobileRegisterUserInfo.getDomain());
            List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
            if (!CollectionUtils.isEmpty(queryRes)) {
                // 手机号已经在该域内被绑定
                log.error("[RegisterServiceImpl.registerByMobile][mobile has binded][mobileRegisterUserInfo={}]",
                        mobileRegisterUserInfo);
                result.setErrMessage("mobile has binded");
                result.setErrCode(UserConstants.ERROR_REGISTER_BAD_MOBILE);
                return result;
            }
            // 执行手机号注册信息落地
            User user = registerBizHandler.doRegisterByMobile(mobileRegisterUserInfo);

            // 构建返回信息
            result.setSuccess(Boolean.TRUE);
            result.setData(UsersConverter.doToVO(user));
            return result;
        } catch (Exception e) {
            log.error("[RegisterServiceImpl.registerByMobile][register exception][mobileRegisterUserInfo="
                    + mobileRegisterUserInfo.toString() + "]", e);
            result.setErrCode(UserConstants.ERROR_REGISTER_FAILED);
            result.setErrMessage("register user failed");
            return result;
        }
    }

    /**
     * 第三方平台用户注册用户
     *
     * @param thirdPartyRegisterUserInfo 第三方注册信息
     * @return 注册成功返回 {@link UserInfoDTO} 对象
     */
    @Override
    public SingleResponse<UserInfoDTO> registerByThirdParty(ThirdPartyRegisterUserInfoDTO thirdPartyRegisterUserInfo) {
        SingleResponse<UserInfoDTO> result = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(thirdPartyRegisterUserInfo.getUname()) || StringUtils.isEmpty(thirdPartyRegisterUserInfo.getThirdId())
                || StringUtils.isEmpty(thirdPartyRegisterUserInfo.getThirdName())
                || null == tenantDomainCacheManager.getTenantDomainByDomainAndAppId(thirdPartyRegisterUserInfo.getDomain(), thirdPartyRegisterUserInfo.getAppId())
                || null == ThirdPartyEnum.getThirdPartyByCode(thirdPartyRegisterUserInfo.getThirdType())) {
            log.error("[RegisterServiceImpl.registerByThirdParty][invalid params][thirdPartyRegisterUserInfo={}]",
                    thirdPartyRegisterUserInfo);
            result.setErrCode(UserConstants.ERROR_PARAM);
            result.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return result;
        }

        try {
            // 检查当前三方账号是否已经绑定
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("thirdId", thirdPartyRegisterUserInfo.getThirdId());
            queryWrapper.eq("thirdType", thirdPartyRegisterUserInfo.getThirdType());
            queryWrapper.eq("appId", thirdPartyRegisterUserInfo.getAppId());
            queryWrapper.eq("domain", thirdPartyRegisterUserInfo.getDomain());
            UserThirdParty queryRes = iUserThirdPartyRepository.getOne(queryWrapper);
            if (null != queryRes) {
                // 三方账号在该域内被绑定
                log.error("[RegisterServiceImpl.registerByThirdParty][thirdParty has bound][thirdPartyRegisterUserInfo={}]",
                        thirdPartyRegisterUserInfo);
                result.setErrMessage("thirdParty has bound");
                result.setErrCode(UserConstants.ERROR_OAUTH_EXISTED);
                return result;
            }
            // 执行邮箱注册信息落地
            User user = registerBizHandler.registerByThirdParty(thirdPartyRegisterUserInfo);
            // 构建返回信息
            result.setSuccess(Boolean.TRUE);
            result.setData(UsersConverter.doToVO(user));
            return result;
        } catch (Exception e) {
            log.error("[RegisterServiceImpl.registerByThirdParty][register exception][thirdPartyRegisterUserInfo="
                    + thirdPartyRegisterUserInfo + "]", e);
            result.setErrCode(UserConstants.ERROR_REGISTER_FAILED);
            result.setErrMessage("register user failed");
            return result;
        }
    }

    /**
     * 通用用户注册方法
     *
     * @param registerUsersInfo 用户注册信息
     * @return 注册成功返回 {@link UserInfoDTO} 对象
     */
    @Override
    public SingleResponse<UserInfoDTO> register(CommonRegisterUserInfoDTO registerUsersInfo) {
        SingleResponse<UserInfoDTO> result = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(registerUsersInfo.getUname())
                || null == tenantDomainCacheManager.getSessionDomainByDomain(registerUsersInfo.getDomain())) {
            log.error("[RegisterServiceImpl.register][invalid params][registerUsersInfo={}]",
                    registerUsersInfo);
            result.setErrCode(UserConstants.ERROR_PARAM);
            result.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return result;
        }

        try {
            // 手机校验
            if (StringUtils.isNotEmpty(registerUsersInfo.getMobile())) {
                if (StringUtils.isEmpty(registerUsersInfo.getAreaCode())) {
                    log.error("[RegisterServiceImpl.register][invalid params][registerUsersInfo={}]",
                            registerUsersInfo);
                    result.setErrCode(UserConstants.ERROR_PARAM);
                    result.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
                    return result;
                }
                // 检查手机号是否已经绑定
                QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("mobile", registerUsersInfo.getMobile());
                queryWrapper.eq("areaCode", registerUsersInfo.getAreaCode());
                queryWrapper.eq("domain", registerUsersInfo.getDomain());
                List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
                if (!CollectionUtils.isEmpty(queryRes)) {
                    // 手机号已经在该域内被绑定
                    log.error("[RegisterServiceImpl.register][mobile has binded][registerUsersInfo={}]",
                            registerUsersInfo);
                    result.setErrMessage("mobile has binded");
                    result.setErrCode(UserConstants.ERROR_REGISTER_BAD_MOBILE);
                    return result;
                }
            }

            // 三方校验
            if (StringUtils.isNotEmpty(registerUsersInfo.getThirdId())) {
                if ((null == ThirdPartyEnum.getThirdPartyByAppId(registerUsersInfo.getAppId()))
                        || (null == ThirdPartyEnum.getThirdPartyByCode(registerUsersInfo.getThirdType()))) {
                    log.error("[RegisterServiceImpl.register][invalid params][registerUsersInfo={}]",
                            registerUsersInfo);
                    result.setErrCode(UserConstants.ERROR_PARAM);
                    result.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
                    return result;
                }
                QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("thirdId", registerUsersInfo.getThirdId());
                queryWrapper.eq("thirdType", registerUsersInfo.getThirdType());
                queryWrapper.eq("domain", registerUsersInfo.getDomain());
                UserThirdParty queryRes = iUserThirdPartyRepository.getOne(queryWrapper);
                if (null != queryRes) {
                    // 三方账号在该域内被绑定
                    log.error("[RegisterServiceImpl.register][thirdParty has binded][registerUsersInfo={}]",
                            registerUsersInfo.toString());
                    result.setErrMessage("thirdParty has bound");
                    result.setErrCode(UserConstants.ERROR_OAUTH_EXISTED);
                    return result;
                }
            }

            // 注册
            User user = registerBizHandler.register(registerUsersInfo);

            // 构建返回信息
            result.setSuccess(Boolean.TRUE);
            result.setData(UsersConverter.doToVO(user));
            return result;
        } catch (Exception e) {
            log.error("[RegisterServiceImpl.register][register exception][userInfo=" + registerUsersInfo + "]", e);
            result.setErrCode(UserConstants.ERROR_REGISTER_FAILED);
            result.setErrMessage("register user failed");
            return result;
        }
    }
}
