package com.neo.user.app.userinfo.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.api.MultiResponse;
import com.neo.user.app.tennant.dto.TenantUserLoginLogDTO;
import com.neo.user.app.tennant.params.TenantUserLoginLogQueryDTO;
import com.neo.user.domain.entity.UserLoginLog;
import com.neo.user.domain.gateway.IUserLoginLogRepository;
import com.neo.user.domain.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class UserLoginLogBizService {

    @Autowired
    private IUserLoginLogRepository iUserLoginLogRepository;

    /**
     * 记录日志
     * @param tenantUserLoginLogDTO
     * @return
     */
    public Boolean addUserLoginLog(TenantUserLoginLogDTO tenantUserLoginLogDTO) {
        UserLoginLog userLoginLog = new UserLoginLog();
        userLoginLog.setTenantId(tenantUserLoginLogDTO.getTenantId());
        userLoginLog.setUserId(tenantUserLoginLogDTO.getUserId());
        userLoginLog.setUname(tenantUserLoginLogDTO.getUname());
        userLoginLog.setUnick(tenantUserLoginLogDTO.getUnick());
        userLoginLog.setEventType(tenantUserLoginLogDTO.getEventType());
        userLoginLog.setDesc(tenantUserLoginLogDTO.getDesc());
        userLoginLog.setResult(tenantUserLoginLogDTO.getResult());
        userLoginLog.setCreated(CommonUtil.getCurrentSeconds());
        userLoginLog.setUpdated(CommonUtil.getCurrentSeconds());
        return iUserLoginLogRepository.save(userLoginLog);
    }

    /**
     * 登录日志
     * @param tenantUserLoginLogQueryDTO
     * @return
     */
    public MultiResponse<TenantUserLoginLogDTO> queryUserLoginLog(TenantUserLoginLogQueryDTO tenantUserLoginLogQueryDTO) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getTenantId, tenantUserLoginLogQueryDTO.getTenantId());
        if (tenantUserLoginLogQueryDTO.getUserId() != null){
            queryWrapper.eq(UserLoginLog::getUserId, tenantUserLoginLogQueryDTO.getUserId());
        }
        if (StringUtils.isNotEmpty(tenantUserLoginLogQueryDTO.getEventType())){
            queryWrapper.eq(UserLoginLog::getEventType, tenantUserLoginLogQueryDTO.getEventType());
        }
        if (StringUtils.isNotEmpty(tenantUserLoginLogQueryDTO.getResult())){
            queryWrapper.eq(UserLoginLog::getResult, tenantUserLoginLogQueryDTO.getResult());
        }
        if (tenantUserLoginLogQueryDTO.getLastLogId() != null){
            queryWrapper.lt(UserLoginLog::getId, tenantUserLoginLogQueryDTO.getLastLogId());
        }
        if (tenantUserLoginLogQueryDTO.getStartTime() != null){
            queryWrapper.ge(UserLoginLog::getCreated, tenantUserLoginLogQueryDTO.getStartTime());
        }
        if (tenantUserLoginLogQueryDTO.getEndTime() != null){
            queryWrapper.le(UserLoginLog::getCreated, tenantUserLoginLogQueryDTO.getEndTime());
        }
        queryWrapper.orderByDesc(UserLoginLog::getId);
        queryWrapper.last("limit " + tenantUserLoginLogQueryDTO.getPageSize());
        List<UserLoginLog> queryList = iUserLoginLogRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(queryList)){
            return MultiResponse.of(new ArrayList<>());
        }
        List<TenantUserLoginLogDTO> list = new ArrayList<>();
        for (UserLoginLog userLoginLog : queryList) {
            TenantUserLoginLogDTO tenantUserLoginLogDTO = new TenantUserLoginLogDTO();
            tenantUserLoginLogDTO.setId(userLoginLog.getId());
            tenantUserLoginLogDTO.setTenantId(userLoginLog.getTenantId());
            tenantUserLoginLogDTO.setUserId(userLoginLog.getUserId());
            tenantUserLoginLogDTO.setUnick(userLoginLog.getUnick());
            tenantUserLoginLogDTO.setUname(userLoginLog.getUname());
            tenantUserLoginLogDTO.setEventType(userLoginLog.getEventType());
            tenantUserLoginLogDTO.setDesc(userLoginLog.getDesc());
            tenantUserLoginLogDTO.setResult(userLoginLog.getResult());
            tenantUserLoginLogDTO.setCreated(userLoginLog.getCreated());
            list.add(tenantUserLoginLogDTO);
        }
        return MultiResponse.of(list);
    }
}
