package com.neo.user.app.userinfo.converter;

import com.neo.user.client.userinfo.dto.ThirdPartyInfoDTO;
import com.neo.user.domain.entity.UserThirdParty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class UsersThirdPartyConverter {
    private static final Logger LOGGER = LoggerFactory.getLogger(UsersThirdPartyConverter.class);

    public static ThirdPartyInfoDTO doToVO(final UserThirdParty userThirdParty) {

        if (null == userThirdParty) {
            return null;
        }
        ThirdPartyInfoDTO thirdPartyInfoDTO = new ThirdPartyInfoDTO();
        thirdPartyInfoDTO.setUserId(userThirdParty.getUserId());
        thirdPartyInfoDTO.setThirdId(userThirdParty.getThirdId());
        thirdPartyInfoDTO.setThirdId2(userThirdParty.getThirdId2());
        thirdPartyInfoDTO.setThirdType(userThirdParty.getThirdType());
        thirdPartyInfoDTO.setThirdName(userThirdParty.getThirdName());
        thirdPartyInfoDTO.setDomain(userThirdParty.getDomain());
        thirdPartyInfoDTO.setAppId(userThirdParty.getAppId());

        return thirdPartyInfoDTO;
    }

    @SuppressWarnings("unchecked")
    public static List<ThirdPartyInfoDTO> doToVOList(final List<UserThirdParty> userThirdParties) {

        if (CollectionUtils.isEmpty(userThirdParties)) {
            return Collections.EMPTY_LIST;
        }
        List<ThirdPartyInfoDTO> list = new ArrayList<ThirdPartyInfoDTO>(userThirdParties.size());
        for(UserThirdParty usersThirdPartyDO: userThirdParties){
            ThirdPartyInfoDTO info = doToVO(usersThirdPartyDO);
        	if(info == null){
        		continue;
        	}
        	list.add(info);
        }
        return list;
    }
}
