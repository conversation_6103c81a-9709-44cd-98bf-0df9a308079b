package com.neo.user.app.userinfo.handler;

import com.alibaba.fastjson.JSON;
import com.neo.user.client.userinfo.dto.BaseRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.CommonRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.MobileRegisterUserInfoDTO;
import com.neo.user.client.userinfo.dto.ThirdPartyRegisterUserInfoDTO;
import com.neo.user.domain.entity.User;
import com.neo.user.domain.entity.UserMobile;
import com.neo.user.domain.entity.UserPwd;
import com.neo.user.domain.entity.UserThirdParty;
import com.neo.user.client.enums.UserMobileStatusEnum;
import com.neo.user.domain.gateway.IUserMobileRepository;
import com.neo.user.domain.gateway.IUserPwdRepository;
import com.neo.user.domain.gateway.IUserRepository;
import com.neo.user.domain.gateway.IUserThirdPartyRepository;
import com.neo.user.domain.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import org.springframework.beans.factory.annotation.Autowired;
import java.util.HashMap;
import java.util.Map;

@Service
public class RegisterBizHandler {

    @Autowired
    private IUserRepository iUserRepository;

    @Autowired
    private IUserMobileRepository iUserMobileRepository;

    @Autowired
    private IUserPwdRepository iUserPwdRepository;

    @Autowired
    private IUserThirdPartyRepository iUserThirdPartyRepository;

    @Transactional(rollbackFor = Exception.class)
    public User doRegisterByMobile(MobileRegisterUserInfoDTO mobileRegisterUserInfoDTO) {
        // 创建用户
        User user = buildUser(mobileRegisterUserInfoDTO);
        iUserRepository.save(user);
        // 创建用户密码
        if (!StringUtils.isEmpty(mobileRegisterUserInfoDTO.getPassword())) {
            UserPwd userPwd = buildUserPwd(mobileRegisterUserInfoDTO, user);
            iUserPwdRepository.save(userPwd);
        }
        // 绑定手机号码
        UserMobile userMobile = buildUserMobile(mobileRegisterUserInfoDTO, user);
        iUserMobileRepository.save(userMobile);
        return user;
    }

    @Transactional(rollbackFor = Exception.class)
    public User registerByThirdParty(ThirdPartyRegisterUserInfoDTO thirdPartyRegisterUserInfoDTO) {
        // 创建用户
        User user = buildUser(thirdPartyRegisterUserInfoDTO);
        iUserRepository.save(user);

        // 绑定第三方信息
        UserThirdParty userThirdParty = buildUserThirdParty(thirdPartyRegisterUserInfoDTO, user);
        iUserThirdPartyRepository.save(userThirdParty);
        return user;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean bindExistUser(ThirdPartyRegisterUserInfoDTO thirdPartyRegisterUserInfoDTO, Long userId){
        User user = new User();
        user.setUserId(userId);
        UserThirdParty userThirdParty = buildUserThirdParty(thirdPartyRegisterUserInfoDTO, user);
        iUserThirdPartyRepository.save(userThirdParty);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public User register(CommonRegisterUserInfoDTO commonRegisterUserInfoDTO) {
        // 创建用户
        User user = buildUser(commonRegisterUserInfoDTO);
        iUserRepository.save(user);

        // 三方信息
        if (StringUtils.isNotEmpty(commonRegisterUserInfoDTO.getThirdId())) {
            UserThirdParty userThirdParty = buildUserThirdPartyByCommon(commonRegisterUserInfoDTO, user);
            iUserThirdPartyRepository.save(userThirdParty);
        }

        // 绑定手机号码
        if (StringUtils.isNotEmpty(commonRegisterUserInfoDTO.getMobile())) {
            UserMobile userMobile = buildUserMobile(commonRegisterUserInfoDTO, user);
            iUserMobileRepository.save(userMobile);
        }

        // 创建密码
        if (StringUtils.isNotEmpty(commonRegisterUserInfoDTO.getPassword())) {
            UserPwd userPwd = buildUserPwd(commonRegisterUserInfoDTO, user);
            iUserPwdRepository.save(userPwd);
        }
        return user;
    }

    private User buildUser(BaseRegisterUserInfoDTO registerUserInfoDTO) {
        User user = new User();
        if (registerUserInfoDTO.getUserId() != null && registerUserInfoDTO.getUserId() > 0) {
            user.setUserId(registerUserInfoDTO.getUserId());
        }
        user.setUname(registerUserInfoDTO.getUname());
        user.setUnick(registerUserInfoDTO.getUnick());
        user.setAvatar(registerUserInfoDTO.getAvatar());
        user.setGender(registerUserInfoDTO.getGender());
        if (CollectionUtils.isEmpty(registerUserInfoDTO.getRegisterInfo())) {
            user.setRegisterInfo("");
        } else {
            user.setRegisterInfo(JSON.toJSONString(registerUserInfoDTO.getRegisterInfo()));
        }
        user.setDomain(registerUserInfoDTO.getDomain());
        user.setStatus(registerUserInfoDTO.getStatus());
        if (!CollectionUtils.isEmpty(registerUserInfoDTO.getUsersExtraInfo())) {
            Map<String, Map<String, Object>> extraData = new HashMap<>();
            extraData.put(registerUserInfoDTO.getDomain(), registerUserInfoDTO.getUsersExtraInfo());
            user.setExtra(JSON.toJSONString(extraData));
        }
        user.setCreated(CommonUtil.getCurrentSeconds());
        user.setUpdated(CommonUtil.getCurrentSeconds());
        return user;
    }

    private UserPwd buildUserPwd(MobileRegisterUserInfoDTO mobileRegisterUserInfoDTO, User user) {
        UserPwd userPwd = new UserPwd();
        userPwd.setUserId(user.getUserId());
        userPwd.setSalt(CommonUtil.getRandomString(10));
        String extra = String.valueOf(user.getUserId());
        userPwd.setExtra(extra);
        userPwd.setDomain(mobileRegisterUserInfoDTO.getDomain());
        userPwd.setPwd(CommonUtil.generatePassword(userPwd.getSalt(), mobileRegisterUserInfoDTO.getPassword(),
                String.valueOf(user.getUserId()), mobileRegisterUserInfoDTO.getDomain()));
        user.setCreated(CommonUtil.getCurrentSeconds());
        user.setUpdated(CommonUtil.getCurrentSeconds());
        return userPwd;
    }

    private UserPwd buildUserPwd(CommonRegisterUserInfoDTO commonRegisterUserInfoDTO, User user) {
        UserPwd userPwd = new UserPwd();
        userPwd.setUserId(user.getUserId());
        userPwd.setSalt(CommonUtil.getRandomString(10));
        String extra = String.valueOf(user.getUserId());
        userPwd.setExtra(extra);
        userPwd.setDomain(commonRegisterUserInfoDTO.getDomain());
        userPwd.setPwd(CommonUtil.generatePassword(userPwd.getSalt(), commonRegisterUserInfoDTO.getPassword(),
                String.valueOf(user.getUserId()), commonRegisterUserInfoDTO.getDomain()));
        user.setCreated(CommonUtil.getCurrentSeconds());
        user.setUpdated(CommonUtil.getCurrentSeconds());
        return userPwd;
    }

    private UserMobile buildUserMobile(MobileRegisterUserInfoDTO mobileRegisterUserInfoDTO, User user) {
        UserMobile userMobile = new UserMobile();
        userMobile.setUserId(user.getUserId());
        userMobile.setAreaCode(mobileRegisterUserInfoDTO.getAreaCode());
        userMobile.setMobile(mobileRegisterUserInfoDTO.getMobile());
        userMobile.setDomain(mobileRegisterUserInfoDTO.getDomain());
        userMobile.setStatus(UserMobileStatusEnum.HAS_VERIFIED.getCode());
        userMobile.setExtra(CollectionUtils.isEmpty(mobileRegisterUserInfoDTO.getMobileExtra()) ? null
                : JSON.toJSONString(mobileRegisterUserInfoDTO.getMobileExtra()));
        user.setCreated(CommonUtil.getCurrentSeconds());
        user.setUpdated(CommonUtil.getCurrentSeconds());
        return userMobile;
    }

    private UserMobile buildUserMobile(CommonRegisterUserInfoDTO commonRegisterUserInfoDTO, User user) {
        UserMobile userMobile = new UserMobile();
        userMobile.setUserId(user.getUserId());
        userMobile.setAreaCode(commonRegisterUserInfoDTO.getAreaCode());
        userMobile.setMobile(commonRegisterUserInfoDTO.getMobile());
        userMobile.setDomain(commonRegisterUserInfoDTO.getDomain());
        userMobile.setStatus(UserMobileStatusEnum.HAS_VERIFIED.getCode());
        userMobile.setExtra(CollectionUtils.isEmpty(commonRegisterUserInfoDTO.getMobileExtra()) ? null
                : JSON.toJSONString(commonRegisterUserInfoDTO.getMobileExtra()));
        userMobile.setCreated(CommonUtil.getCurrentSeconds());
        userMobile.setUpdated(CommonUtil.getCurrentSeconds());
        return userMobile;
    }

    private UserThirdParty buildUserThirdParty(ThirdPartyRegisterUserInfoDTO thirdPartyRegisterUserInfoDTO, User user) {
        if (StringUtils.isEmpty(thirdPartyRegisterUserInfoDTO.getAppId()) || StringUtils.isEmpty(thirdPartyRegisterUserInfoDTO.getThirdId())
                || thirdPartyRegisterUserInfoDTO.getThirdType() <= 0) {
            return null;
        }
        UserThirdParty userThirdParty = new UserThirdParty();
        userThirdParty.setUserId(user.getUserId());
        userThirdParty.setThirdName(thirdPartyRegisterUserInfoDTO.getThirdName());
        userThirdParty.setThirdId(thirdPartyRegisterUserInfoDTO.getThirdId());
        userThirdParty.setThirdId2(thirdPartyRegisterUserInfoDTO.getThirdId2());
        userThirdParty.setThirdType(thirdPartyRegisterUserInfoDTO.getThirdType());
        userThirdParty.setAppId(thirdPartyRegisterUserInfoDTO.getAppId());
        userThirdParty.setAccessToken(thirdPartyRegisterUserInfoDTO.getAccessToken());
        userThirdParty.setRefreshToken(thirdPartyRegisterUserInfoDTO.getRefreshToken());
        userThirdParty.setExpireTime(thirdPartyRegisterUserInfoDTO.getExpireTime());
        userThirdParty.setDomain(thirdPartyRegisterUserInfoDTO.getDomain());
        userThirdParty.setCreated(CommonUtil.getCurrentSeconds());
        userThirdParty.setUpdated(CommonUtil.getCurrentSeconds());
        return userThirdParty;
    }

    private UserThirdParty buildUserThirdPartyByCommon(CommonRegisterUserInfoDTO commonRegisterUserInfoDTO, User user) {
        if (StringUtils.isEmpty(commonRegisterUserInfoDTO.getAppId()) || StringUtils.isEmpty(commonRegisterUserInfoDTO.getThirdId())
                || commonRegisterUserInfoDTO.getThirdType() <= 0) {
            return null;
        }
        UserThirdParty userThirdParty = new UserThirdParty();
        userThirdParty.setUserId(user.getUserId());
        userThirdParty.setThirdName(commonRegisterUserInfoDTO.getThirdName());
        userThirdParty.setThirdId(commonRegisterUserInfoDTO.getThirdId());
        userThirdParty.setThirdId2(commonRegisterUserInfoDTO.getThirdId2());
        userThirdParty.setThirdType(commonRegisterUserInfoDTO.getThirdType());
        userThirdParty.setAppId(commonRegisterUserInfoDTO.getAppId());
        userThirdParty.setAccessToken(commonRegisterUserInfoDTO.getAccessToken());
        userThirdParty.setRefreshToken(commonRegisterUserInfoDTO.getRefreshToken());
        userThirdParty.setExpireTime(commonRegisterUserInfoDTO.getExpireTime());
        userThirdParty.setDomain(commonRegisterUserInfoDTO.getDomain());
        userThirdParty.setCreated(CommonUtil.getCurrentSeconds());
        userThirdParty.setUpdated(CommonUtil.getCurrentSeconds());
        return userThirdParty;
    }
}
