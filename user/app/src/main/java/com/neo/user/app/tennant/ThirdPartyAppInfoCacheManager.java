package com.neo.user.app.tennant;

import com.neo.user.client.userinfo.dto.EnumThirdPartyAppInfoDTO;
import com.neo.user.domain.gateway.cache.EnumThirdPartyAppInfoCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("thirdPartyAppInfoCacheManager")
@Slf4j
public class ThirdPartyAppInfoCacheManager implements EnumThirdPartyAppInfoCacheService {

    @Autowired
    private EnumThirdPartyAppInfoCacheService enumThirdPartyAppInfoLocalCacheImpl;

    @Override
    public EnumThirdPartyAppInfoDTO getByAppIdAndThirdPlatform(String appId, String thridPlatform) {
        return enumThirdPartyAppInfoLocalCacheImpl.getByAppIdAndThirdPlatform(appId, thridPlatform);
    }
}
