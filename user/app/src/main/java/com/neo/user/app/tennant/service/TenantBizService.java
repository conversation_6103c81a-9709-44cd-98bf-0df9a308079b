package com.neo.user.app.tennant.service;

import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.dto.TenantManageDTO;
import com.neo.user.app.tennant.dto.TenantUserUpdateDTO;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.app.tennant.dto.TenantUserRegisterDTO;
import com.neo.user.app.tennant.params.TenantUserQueryDTO;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import com.neo.user.app.tennant.dto.TenantDTO;

public interface TenantBizService {

    public SingleResponse<TenantDTO> getById(Long id);

    public SingleResponse<Long> createTenant(TenantManageDTO tenantManageDTO);

    public SingleResponse<Boolean> update(TenantDTO tenantDTO);

    public PageResponse<TenantUserInfoDTO> queryTenantUser(TenantUserQueryDTO tenantUserQueryDTO);

    public MultiResponse<DepartmentDTO> getAllDepartment(Long tenantId);

    public MultiResponse<TenantUserInfoDTO> fuzzyQueryUser(Long tenantId, String uname, Boolean includeDeleted);

    public SingleResponse<Boolean> addDepartment(DepartmentDTO departmentDTO);

    public SingleResponse<Boolean> updateDepartment(DepartmentDTO departmentDTO);

    public SingleResponse<Boolean> deleteDepartment(Long tenantId, Long deptId);

    public SingleResponse<TenantUserInfoDTO> addUser(TenantUserRegisterDTO tenantUserRegisterDTO);

    public SingleResponse<TenantUserInfoDTO> getUserByUserId(Long tenantId, Long userId);

    public SingleResponse<Boolean> updateUser(TenantUserUpdateDTO tenantUserUpdateDTO);

    public SingleResponse<Boolean> deleteUser(Long tenantId, Long userId);

    public SingleResponse<Boolean> bindApp(Long tenantId, Long userId, String appId);
}
