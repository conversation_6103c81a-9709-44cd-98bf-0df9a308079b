package com.neo.user.app.userinfo.biz;

import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.user.app.tennant.dto.TenantUserLoginLogDTO;
import com.neo.user.app.userinfo.impl.PasswordServiceImpl;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.app.verify.impl.SessionServiceImpl;
import com.neo.user.client.tenant.dto.EnumTenantDomainDTO;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.gateway.cache.EnumTenantDomainCacheService;
import com.neo.user.domain.gateway.cache.UserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.neo.user.domain.constants.UserConstants.*;
import static com.neo.user.domain.constants.UserConstants.CACHE_PWD_ERROR_FORBIDDEN_SUFFIX;

@Service
@Slf4j
public class PasswordBizService {

    @Autowired
    private PasswordServiceImpl passwordServiceImpl;

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private SessionServiceImpl sessionServiceImpl;

    @Autowired
    private UserCacheService userCacheService;

    @Autowired
    private EnumTenantDomainCacheService tenantDomainCacheManager;

    @Autowired
    private UserLoginLogBizService userLoginLogBizService;

    public SingleResponse<Boolean> changePassword(Long userId, String oldPassword, String newPassword){
        SingleResponse<UserInfoDTO> userRes = userServiceImpl.queryUserByUserId(userId);
        if (!userRes.isSuccess()){
            return SingleResponse.buildFailure(userRes.getErrCode(), userRes.getErrMessage());
        }
        UserInfoDTO userInfoDTO = userRes.getData();
        SingleResponse<Boolean> isSet = passwordServiceImpl.isPasswordSet(userId, userInfoDTO.getDomain());
        if (!isSet.isSuccess() || !isSet.getData()){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "未设置密码，无法修改");
        }

        SingleResponse<Boolean> checkRes = passwordServiceImpl.checkPassword(userId, oldPassword, userInfoDTO.getDomain());
        if (!checkRes.isSuccess()){
            if (checkRes.getErrCode().equals(ERROR_LOGIN_WRONG_PWD)){
                return SingleResponse.buildFailure(checkRes.getErrCode(), "原密码错误");
            }
            return SingleResponse.buildFailure(checkRes.getErrCode(), checkRes.getErrMessage());
        }
        if (!checkRes.getData()){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "原密码错误");
        }
        SingleResponse<Boolean> setRes = passwordServiceImpl.setPassword(userId, newPassword, userInfoDTO.getDomain());
        if (setRes.isSuccess()){
            sessionServiceImpl.clearSession(userId);//修改密码成功，失效当前用户所有已登录sign
            clearPasswordErrorCount(userId);

            EnumTenantSessionDomainDTO bizDomain = tenantDomainCacheManager.getSessionDomainByDomain(userInfoDTO.getDomain());
            //记录日志
            TenantUserLoginLogDTO tenantUserLoginLogDTO = new TenantUserLoginLogDTO();
            tenantUserLoginLogDTO.setUserId(userInfoDTO.getUserId());
            tenantUserLoginLogDTO.setTenantId(bizDomain.getTenantId());
            tenantUserLoginLogDTO.setUname(userInfoDTO.getUname());
            tenantUserLoginLogDTO.setUnick(userInfoDTO.getUnick());
            tenantUserLoginLogDTO.setEventType(EVENT_TYPE_PWD_CHANGE);
            tenantUserLoginLogDTO.setDesc(EVENT_TYPE_PWD_CHANGE);
            tenantUserLoginLogDTO.setResult(RESULT_SUCCESS);
            userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);

            return SingleResponse.buildSuccess(setRes.getData());
        }
        return SingleResponse.buildFailure(setRes.getErrCode(), "设置新密码失败");
    }

    public SingleResponse<Boolean> changePasswordByAdmin(Long userId, String newPassword){
        SingleResponse<UserInfoDTO> userRes = userServiceImpl.queryUserByUserId(userId);
        if (!userRes.isSuccess()){
            return SingleResponse.buildFailure(userRes.getErrCode(), userRes.getErrMessage());
        }
        UserInfoDTO userInfoDTO = userRes.getData();
//        if (!NEO_APP.getDomainName().equals(userInfoDTO.getDomain())){
//            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "该账号不支持设置密码");
//        }
        SingleResponse<Boolean> setRes = passwordServiceImpl.setPassword(userId, newPassword, userInfoDTO.getDomain());
        if (setRes.isSuccess()){
            sessionServiceImpl.clearSession(userId);//修改密码成功，失效当前用户所有已登录sign
            clearPasswordErrorCount(userId);

            EnumTenantSessionDomainDTO bizDomain = tenantDomainCacheManager.getSessionDomainByDomain(userInfoDTO.getDomain());
            //记录日志
            TenantUserLoginLogDTO tenantUserLoginLogDTO = new TenantUserLoginLogDTO();
            tenantUserLoginLogDTO.setUserId(userInfoDTO.getUserId());
            tenantUserLoginLogDTO.setTenantId(bizDomain.getTenantId());
            tenantUserLoginLogDTO.setUname(userInfoDTO.getUname());
            tenantUserLoginLogDTO.setUnick(userInfoDTO.getUnick());
            tenantUserLoginLogDTO.setEventType(EVENT_TYPE_PWD_CHANGE);
            tenantUserLoginLogDTO.setDesc(EVENT_TYPE_PWD_CHANGE_ADMIN);
            tenantUserLoginLogDTO.setResult(RESULT_SUCCESS);
            userLoginLogBizService.addUserLoginLog(tenantUserLoginLogDTO);

            return SingleResponse.buildSuccess(setRes.getData());
        }
        return SingleResponse.buildFailure(setRes.getErrCode(), "设置新密码失败");
    }

    public Boolean checkPasswordErrorCount(Long userId){
        String isForbidden = (String) userCacheService.get(userId + CACHE_PWD_ERROR_FORBIDDEN_SUFFIX);// 锁定1小时
        // 错误达5次，提示锁定1小时
        return StringUtils.isNotEmpty(isForbidden);
    }

    public Long incrPasswordErrorCount(Long userId){
        String errorCountKey = userId + CACHE_PWD_ERROR_COUNT_SUFFIX;
        String forbiddenKey = userId + CACHE_PWD_ERROR_FORBIDDEN_SUFFIX;
        Long errorCount = userCacheService.increx(errorCountKey, 1L, 24 * 60 * 60);
        if (errorCount != null && errorCount >= 5 && userCacheService.get(forbiddenKey) == null) {
            userCacheService.increx(errorCountKey, 0L, 3600);// 计数器剩余时间也重置为1小时
            userCacheService.setex(forbiddenKey, String.valueOf(errorCount), 3600);// 锁定1小时
        }
        return errorCount;
    }

    public void clearPasswordErrorCount(Long userId){
        String errorCountKey = userId + CACHE_PWD_ERROR_COUNT_SUFFIX;
        String forbiddenKey = userId + CACHE_PWD_ERROR_FORBIDDEN_SUFFIX;
        userCacheService.delete(errorCountKey);
        userCacheService.delete(forbiddenKey);
    }

}
