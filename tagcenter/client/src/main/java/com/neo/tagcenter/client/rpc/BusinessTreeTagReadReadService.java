package com.neo.tagcenter.client.rpc;

import com.neo.api.MultiResponse;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;

/**
 * 业务标签读服务，提供具体接口
 */
public interface BusinessTreeTagReadReadService {

    /**
     * 销售区域设置业务 标签查询
     * <p>
     * businessDomainEnum 即租户id（下同）
     */
    MultiResponse<TagLeafInfoDto> querySalesRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 行政区域设置
     */
    MultiResponse<TagLeafInfoDto> queryAdministrativeRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 客户类型设置
     */
    MultiResponse<TagLeafInfoDto> queryCustomerTypeSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 价格方案设置
     */
    MultiResponse<TagLeafInfoDto> queryPricePlanSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 送货线路
     */
    MultiResponse<TagLeafInfoDto> queryDeliveryRouteSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);


    /**
     * 货品分类设置
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-成品
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-半成品
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingSemiFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-原材料
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingRawMaterial(BusinessTreeTagQueryParam param, BaseTagQueryOption option);


}
