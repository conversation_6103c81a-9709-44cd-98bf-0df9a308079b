package com.neo.tagcenter.client.param;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TreeTagSaveParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 6564872713953610045L;

    /**
     * 业务域
     */
    private Integer businessDomain;

    /**
     * 标签域
     */
    private String tagDomain;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 名称简写
     */
    private String shortName;

    /**
     * 三方创建时间（时间戳）
     */
    private Long thirdPartyCreateTime;

    /**
     * 三方创建人
     */
    private String thirdPartyCreator;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 该叶子节点父叶子节点
     */
    private Long parentId;

    /**
     * 该叶子结点的扩展信息
     */
    private String extra;

    /**
     * outId
     */
    private String outId1;

    /**
     * outId
     */
    private String outId2;

    /**
     * outId
     */
    private String outId3;

    /**
     * 外部父节点
     */
    private String outParentId1;

    /**
     * 外部父节点
     */
    private String outParentId2;

    /**
     * 外部父节点
     */
    private String outParentId3;

    // ---------- option -----------


    /**
     * 校验参数
     */
    public String validate() {
        if (businessDomain == null ||
                StrUtil.isBlank(name) ||
                (parentId == null || parentId < 0)) {
            return "参数错误";
        }

        if (isRootTag()) {
            if (StrUtil.isBlank(tagDomain)) {
                return "根节点必须有标签域";
            }
        }

        if (creator == null) {
            return "创建者不能为空";
        }

        return null;
    }

    /**
     * 判断是否是根节点
     */
    public boolean isRootTag() {
        return parentId == 0;
    }
}
