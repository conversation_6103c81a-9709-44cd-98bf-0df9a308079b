package com.neo.tagcenter.client.rpc;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BaseTreeTagQueryOption;
import com.neo.tagcenter.client.param.TreeTagQueryOption;

import java.util.List;
import java.util.Map;

public interface TreeTagReadService {


    /**
     * 根据 业务域 查询标签节点
     * 业务域：businessDomain，用来圈定多棵树
     * 理论上 业务域的范围 大于 标签域的范围
     * <p>
     * option：额外查询条件
     * includeDeleted是否包含删除，默认不包含删除；includeDisable是否包含不可用，默认不包含不可用
     *
     * @return Map<业务域, List < 标签节点>>，标签节点平铺展示
     */
    SingleResponse<Map<Integer, List<TagLeafInfoDto>>> queryTagLeafInfoByBusinessDomain(List<Integer> businessDomainEnumList, BaseTagQueryOption option);

    /**
     * 根据 标签域 查询标签节点
     * 标签域：tagDomain，用来圈定单棵树或者多棵树
     * <p>
     * option：额外查询条件
     * includeDeleted是否包含删除，默认不包含删除；includeDisable是否包含不可用，默认不包含不可用
     *
     * @return Map<标签域, List < 标签节点>>，标签节点平铺展示
     */
    SingleResponse<Map<String, List<TagLeafInfoDto>>> queryTagLeafInfoByTagDomain(List<String> tagDomainEnumList, BaseTagQueryOption option);


    /**
     * 根据 业务域+标签域 查询标签节点
     * 业务域+标签域：用来圈定单棵树
     * <p>
     * option：额外查询条件
     * includeDeleted是否包含删除，默认不包含删除；includeDisable是否包含不可用，默认不包含不可用
     *
     * @return List<标签节点>，标签节点平铺展示
     */
    MultiResponse<TagLeafInfoDto> queryTagLeafInfoByBusinessDomainAndTagDomain(Integer businessDomain, String tagDomain, BaseTagQueryOption option);

    /**
     * 根据 标签ID 查询标签节点
     * <p>
     * option：额外查询条件
     * includeDeleted是否包含删除，默认不包含删除；includeDisable是否包含不可用，默认不包含不可用；queryChild是否需要查询子节点，默认不查询子节点
     *
     * @return 标签节点平铺展示
     */
    MultiResponse<TagLeafInfoDto> queryTagLeafInfoById(Long tagLeafId, BaseTreeTagQueryOption option);

    /**
     * 根据 标签Code 获取标签节点
     */
    MultiResponse<TagLeafInfoDto> queryTagLeafInfoByTagCode(Integer businessDomain, String tagDomain, String tagCode, BaseTreeTagQueryOption option);

    /**
     * 根据 名称 获取标签节点
     */
    MultiResponse<TagLeafInfoDto> queryTagLeafInfoByTagName(Integer businessDomain, String tagDomain, String tagName, BaseTreeTagQueryOption option);

    /**
     * 复杂查询，查询标签列表
     */
    MultiResponse<TagLeafInfoDto> queryTagLeafInfo(TreeTagQueryOption option);
}
